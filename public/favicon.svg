<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff00ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="32" height="32" fill="#0a0a0a" rx="3.2"/>
  
  <!-- Logo B -->
  <text x="50%" y="50%" 
        font-family="Arial, sans-serif" 
        font-size="19.2" 
        font-weight="bold" 
        text-anchor="middle" 
        dominant-baseline="central" 
        fill="url(#grad)">B</text>
  
  <!-- Glow effect -->
  <circle cx="16" cy="16" r="12.8" 
          fill="none" 
          stroke="#00ffff" 
          stroke-width="2" 
          opacity="0.3"/>
</svg>