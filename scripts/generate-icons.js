/**
 * Script para gerar ícones PWA faltantes
 * Cria ícones simples com o logo do Blueprint Blog
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Criar diretório de ícones se não existir
const iconsDir = path.join(__dirname, '..', 'public', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Template SVG base para os ícones
const createIconSVG = (size) => `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff00ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="${size}" height="${size}" fill="#0a0a0a" rx="${size * 0.1}"/>
  
  <!-- Logo B -->
  <text x="50%" y="50%" 
        font-family="Arial, sans-serif" 
        font-size="${size * 0.6}" 
        font-weight="bold" 
        text-anchor="middle" 
        dominant-baseline="central" 
        fill="url(#grad)">B</text>
  
  <!-- Glow effect -->
  <circle cx="${size/2}" cy="${size/2}" r="${size * 0.4}" 
          fill="none" 
          stroke="#00ffff" 
          stroke-width="2" 
          opacity="0.3"/>
</svg>`;

// Tamanhos necessários para PWA
const iconSizes = [16, 32, 48, 72, 96, 128, 144, 152, 192, 384, 512];

// Gerar ícones SVG
iconSizes.forEach(size => {
  const svgContent = createIconSVG(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`✅ Criado: ${filename}`);
});

// Criar favicon.ico (cópia do 32x32)
const faviconPath = path.join(__dirname, '..', 'public', 'favicon.ico');
const favicon32Path = path.join(iconsDir, 'icon-32x32.svg');

if (fs.existsSync(favicon32Path)) {
  fs.copyFileSync(favicon32Path, faviconPath.replace('.ico', '.svg'));
  console.log('✅ Criado: favicon.svg');
}

// Criar apple-touch-icon
const appleTouchIcon = createIconSVG(180);
fs.writeFileSync(path.join(iconsDir, 'apple-touch-icon.svg'), appleTouchIcon);
console.log('✅ Criado: apple-touch-icon.svg');

// Criar safari-pinned-tab
const safariIcon = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="180" height="180" viewBox="0 0 180 180" xmlns="http://www.w3.org/2000/svg">
  <path d="M20 20h140v140H20z" fill="#000"/>
  <text x="90" y="90" font-family="Arial" font-size="100" font-weight="bold" text-anchor="middle" dominant-baseline="central" fill="#fff">B</text>
</svg>`;

fs.writeFileSync(path.join(iconsDir, 'safari-pinned-tab.svg'), safariIcon);
console.log('✅ Criado: safari-pinned-tab.svg');

console.log('\n🎉 Todos os ícones PWA foram gerados com sucesso!');
console.log('📁 Localização:', iconsDir);
