{"name": "blueprint-blog-v2", "private": true, "version": "2.0.0", "type": "module", "description": "Blueprint Blog v2 - Cyberpunk Neon Blog with React + Vite + TypeScript", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "@vercel/analytics": "^1.5.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.515.0", "motion": "^12.18.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "npm:@slorber/react-helmet-async@1.3.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router": "^7.6.2", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "supabase": "^2.24.3", "tailwind-merge": "^3.3.1", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@rollup/plugin-terser": "^0.4.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.5", "tailwindcss": "^3.4.17", "terser": "^5.42.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}