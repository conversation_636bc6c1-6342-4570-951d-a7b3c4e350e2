/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      // Mobile-first breakpoints
      screens: {
        'xs': '320px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
        // Mobile-specific breakpoints
        'mobile-xs': {'max': '480px'},
        'mobile-sm': {'min': '480px', 'max': '640px'},
        'mobile-lg': {'min': '640px', 'max': '768px'},
        'tablet': {'min': '768px', 'max': '1024px'},
      },
      colors: {
        // Cyberpunk Neon Theme
        neon: {
          cyan: '#00ffff',
          magenta: '#ff00ff',
          purple: '#8b5cf6',
          blue: '#0066ff',
          green: '#00ff88',
          pink: '#ff0080',
          yellow: '#ffff00',
        },
        // Avatar Blueprint Theme
        blueprint: {
          'bg-start': '#667eea',
          'bg-end': '#764ba2',
          'accent-start': '#4facfe',
          'accent-end': '#00f2fe',
          'code-start': '#ff9a9e',
          'code-end': '#fecfef',
        },
        dark: {
          900: '#0a0a0a',
          800: '#1a1a1a',
          700: '#2a2a2a',
          600: '#3a3a3a',
          500: '#4a4a4a',
        },
        cyber: {
          bg: '#0d1117',
          surface: '#161b22',
          border: '#30363d',
          text: '#f0f6fc',
          muted: '#8b949e',
        },
      },
      fontFamily: {
        sans: ['Nunito Sans', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace'],
      },
      // Mobile Typography Extensions
      fontSize: {
        'xs-mobile': ['clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem)', '1.4'],
        'sm-mobile': ['clamp(0.875rem, 0.8rem + 0.375vw, 1rem)', '1.4'],
        'base-mobile': ['clamp(1rem, 0.9rem + 0.5vw, 1.125rem)', '1.4'],
        'lg-mobile': ['clamp(1.125rem, 1rem + 0.625vw, 1.25rem)', '1.3'],
        'xl-mobile': ['clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem)', '1.3'],
        '2xl-mobile': ['clamp(1.5rem, 1.3rem + 1vw, 1.875rem)', '1.2'],
        '3xl-mobile': ['clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem)', '1.2'],
        '4xl-mobile': ['clamp(2.25rem, 1.9rem + 1.75vw, 3rem)', '1.1'],
      },
      // Mobile Spacing
      spacing: {
        'mobile-xs': '0.25rem',
        'mobile-sm': '0.5rem',
        'mobile-md': '0.75rem',
        'mobile-lg': '1rem',
        'mobile-xl': '1.5rem',
        'mobile-2xl': '2rem',
      },
      animation: {
        glow: 'glow 2s ease-in-out infinite alternate',
        'pulse-neon': 'pulse-neon 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        float: 'float 3s ease-in-out infinite',
        glitch: 'glitch 0.3s ease-in-out infinite',
        marquee: 'marquee 25s linear infinite',
        'marquee-reverse': 'marquee-reverse 25s linear infinite',
        'scan-horizontal': 'scan-horizontal 3s ease-in-out infinite',
        'scan-vertical': 'scan-vertical 4s ease-in-out infinite',
      },
      keyframes: {
        glow: {
          '0%': {
            boxShadow: '0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff',
          },
          '100%': {
            boxShadow: '0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px #00ffff',
          },
        },
        'pulse-neon': {
          '0%, 100%': {
            opacity: '1',
            filter: 'drop-shadow(0 0 10px #00ffff)',
          },
          '50%': {
            opacity: '0.8',
            filter: 'drop-shadow(0 0 20px #ff00ff)',
          },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glitch: {
          '0%': { transform: 'translate(0)' },
          '20%': { transform: 'translate(-2px, 2px)' },
          '40%': { transform: 'translate(-2px, -2px)' },
          '60%': { transform: 'translate(2px, 2px)' },
          '80%': { transform: 'translate(2px, -2px)' },
          '100%': { transform: 'translate(0)' },
        },
        marquee: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        'marquee-reverse': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'scan-horizontal': {
          '0%': { left: '-100%' },
          '100%': { left: '100%' },
        },
        'scan-vertical': {
          '0%': { top: '-100%' },
          '100%': { top: '100%' },
        },
      },
      backgroundImage: {
        'gradient-neon': 'linear-gradient(45deg, #00ffff, #ff00ff, #8b5cf6)',
        'gradient-cyber': 'linear-gradient(135deg, #0d1117 0%, #161b22 100%)',
      },
    },
  },
  plugins: [],
};
