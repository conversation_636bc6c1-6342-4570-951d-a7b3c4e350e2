import { logger } from './logger';

export class ComponentLogger {
  private static isProduction = process.env.NODE_ENV === 'production';

  static logMount(componentName: string, props?: Record<string, any>): void {
    // Em produção, não fazer log de mount de componentes
    if (this.isProduction) return;

    logger.debug('Component Mounted', {
      type: 'component_lifecycle',
      component: componentName,
      event: 'mount',
      props,
    });
  }

  static logUnmount(componentName: string): void {
    // Em produção, não fazer log de unmount de componentes
    if (this.isProduction) return;

    logger.debug('Component Unmounted', {
      type: 'component_lifecycle',
      component: componentName,
      event: 'unmount',
    });
  }

  static logStateChange(
    componentName: string,
    oldState: any,
    newState: any
  ): void {
    // Em produção, não fazer log de mudanças de estado
    if (this.isProduction) return;

    logger.debug('State Change', {
      type: 'state_change',
      component: componentName,
      oldState,
      newState,
    });
  }

  static logError(componentName: string, error: Error, errorInfo?: any): void {
    logger.error('Component Error', {
      type: 'component_error',
      component: componentName,
      error: error.message,
      stack: error.stack,
      errorInfo,
    });
  }
}
