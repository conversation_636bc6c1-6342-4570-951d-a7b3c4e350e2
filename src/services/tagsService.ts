import type { Tag } from '../hooks/useTags';
import { supabase } from '../lib/supabaseClient';

export interface CreateTagData {
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  is_active?: boolean;
}

export interface UpdateTagData extends Partial<CreateTagData> {
  id: string;
}

export interface TagsResponse<T> {
  data: T | null;
  error: string | null;
}

export class TagsService {
  /**
   * Busca todas as tags (incluindo inativas para admin)
   */
  async getAllTags(): Promise<TagsResponse<Tag[]>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        console.error('Erro ao buscar tags:', error);
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar tags:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Busca uma tag por ID
   */
  async getTagById(id: string): Promise<TagsResponse<Tag>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Erro ao buscar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao buscar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Cria uma nova tag
   */
  async createTag(tagData: CreateTagData): Promise<TagsResponse<Tag>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .insert([
          {
            ...tagData,
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) {
        console.error('Erro ao criar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao criar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Atualiza uma tag existente
   */
  async updateTag(tagData: UpdateTagData): Promise<TagsResponse<Tag>> {
    try {
      const { id, ...updateData } = tagData;

      const { data, error } = await supabase
        .from('tags')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao atualizar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao atualizar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Deleta uma tag (soft delete)
   */
  async deleteTag(id: string): Promise<TagsResponse<null>> {
    try {
      const { error } = await supabase
        .from('tags')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        console.error('Erro ao deletar tag:', error);
        return { data: null, error: error.message };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Erro inesperado ao deletar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Reativa uma tag
   */
  async reactivateTag(id: string): Promise<TagsResponse<Tag>> {
    try {
      const { data, error } = await supabase
        .from('tags')
        .update({ is_active: true })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Erro ao reativar tag:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Erro inesperado ao reativar tag:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Busca estatísticas de uso das tags
   */
  async getTagStats(): Promise<
    TagsResponse<Array<{ tag_id: string; post_count: number }>>
  > {
    try {
      console.log('🔍 [TagsService] Iniciando busca de estatísticas...');

      // Primeiro, tentar buscar da tabela post_tags (sistema novo)
      const { data: relationData, error: relationError } = await supabase
        .from('post_tags')
        .select('tag_id');

      console.log('📊 [TagsService] Dados da tabela post_tags:', {
        count: relationData?.length || 0,
        error: relationError?.message,
        data: relationData,
      });

      if (!relationError && relationData && relationData.length > 0) {
        // Se há dados na tabela post_tags, usar o sistema novo
        console.log('✅ [TagsService] Usando sistema novo (post_tags)');

        const tagCounts = relationData.reduce(
          (acc: Record<string, number>, item) => {
            acc[item.tag_id] = (acc[item.tag_id] || 0) + 1;
            return acc;
          },
          {}
        );

        const stats = Object.entries(tagCounts).map(([tag_id, post_count]) => ({
          tag_id,
          post_count: post_count as number,
        }));

        console.log('📈 [TagsService] Estatísticas (sistema novo):', stats);
        return { data: stats, error: null };
      }

      // Se não há dados na post_tags, retornar array vazio
      // O campo posts.tags foi removido na migração para estrutura normalizada
      console.log('⚠️ [TagsService] Tabela post_tags vazia - retornando estatísticas vazias');
      console.log('💡 [TagsService] Execute a migração para popular post_tags com dados dos posts');

      return { data: [], error: null };


    } catch (error) {
      console.error('Erro inesperado ao buscar estatísticas:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }

  /**
   * Verifica se um slug já existe
   */
  async checkSlugExists(
    slug: string,
    excludeId?: string
  ): Promise<TagsResponse<boolean>> {
    try {
      let query = supabase.from('tags').select('id').eq('slug', slug);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Erro ao verificar slug:', error);
        return { data: null, error: error.message };
      }

      return { data: (data?.length || 0) > 0, error: null };
    } catch (error) {
      console.error('Erro inesperado ao verificar slug:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      };
    }
  }
}

// Instância singleton
export const tagsService = new TagsService();
