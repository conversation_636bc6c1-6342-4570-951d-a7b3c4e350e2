/**
 * @fileoverview Hook para busca de posts no header
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useState, useEffect, useMemo } from 'react';
import { usePosts } from './usePosts';
import { useAnalytics } from './useAnalytics';
import type { Post } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface SearchResult {
  post: Post;
  relevance: number;
  matchType: 'title' | 'excerpt' | 'author' | 'category' | 'tag';
}

interface UsePostSearchOptions {
  maxResults?: number;
  minQueryLength?: number;
  debounceMs?: number;
}

interface UsePostSearchReturn {
  query: string;
  setQuery: (query: string) => void;
  results: SearchResult[];
  loading: boolean;
  hasResults: boolean;
  clearSearch: () => void;
  executeSearch: (searchQuery: string) => void;
}

// ============================================================================
// HOOK
// ============================================================================

/**
 * Hook para busca inteligente de posts com relevância
 */
export const usePostSearch = (options: UsePostSearchOptions = {}): UsePostSearchReturn => {
  const {
    maxResults = 8,
    minQueryLength = 2,
    debounceMs = 300
  } = options;

  const { trackSearch } = useAnalytics();
  const { posts } = usePosts({ status: 'published' });

  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [loading, setLoading] = useState(false);

  // Debounce da query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [query, debounceMs]);

  // Função para calcular relevância
  const calculateRelevance = (post: Post, searchQuery: string): SearchResult | null => {
    const lowerQuery = searchQuery.toLowerCase();
    const lowerTitle = post.title.toLowerCase();
    const lowerExcerpt = post.excerpt.toLowerCase();
    const lowerAuthor = post.author.name.toLowerCase();
    const lowerCategory = post.category.name.toLowerCase();

    let relevance = 0;
    let matchType: SearchResult['matchType'] = 'excerpt';

    // Título (maior peso)
    if (lowerTitle.includes(lowerQuery)) {
      relevance += 100;
      matchType = 'title';
      
      // Bonus se for match exato
      if (lowerTitle === lowerQuery) {
        relevance += 50;
      }
      
      // Bonus se começar com a query
      if (lowerTitle.startsWith(lowerQuery)) {
        relevance += 25;
      }
    }

    // Excerpt (peso médio)
    if (lowerExcerpt.includes(lowerQuery)) {
      relevance += 50;
      if (matchType === 'excerpt') matchType = 'excerpt';
    }

    // Autor (peso médio)
    if (lowerAuthor.includes(lowerQuery)) {
      relevance += 40;
      if (relevance === 40) matchType = 'author';
    }

    // Categoria (peso baixo)
    if (lowerCategory.includes(lowerQuery)) {
      relevance += 30;
      if (relevance === 30) matchType = 'category';
    }

    // Tags (peso baixo)
    const hasTagMatch = post.tags?.some(tag => 
      tag.name.toLowerCase().includes(lowerQuery)
    );
    if (hasTagMatch) {
      relevance += 20;
      if (relevance === 20) matchType = 'tag';
    }

    // Bonus para posts populares
    if (post.views > 1000) relevance += 10;
    if (post.likes > 50) relevance += 5;
    if (post.featured) relevance += 15;

    // Bonus para posts recentes (últimos 30 dias)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    if (new Date(post.date) > thirtyDaysAgo) {
      relevance += 10;
    }

    return relevance > 0 ? { post, relevance, matchType } : null;
  };

  // Busca com relevância
  const results = useMemo(() => {
    if (!debouncedQuery || debouncedQuery.length < minQueryLength) {
      return [];
    }

    setLoading(true);

    const searchResults: SearchResult[] = [];

    posts.forEach(post => {
      const result = calculateRelevance(post, debouncedQuery);
      if (result) {
        searchResults.push(result);
      }
    });

    // Ordenar por relevância (maior primeiro)
    const sortedResults = searchResults
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, maxResults);

    setLoading(false);

    // Track search se houver resultados
    if (sortedResults.length > 0) {
      trackSearch(debouncedQuery);
    }

    return sortedResults;
  }, [debouncedQuery, posts, maxResults, minQueryLength, trackSearch]);

  const executeSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      // Redirecionar para página de blog com busca
      const searchParams = new URLSearchParams();
      searchParams.set('search', searchQuery.trim());
      window.location.href = `/blog?${searchParams.toString()}`;
    }
  };

  const clearSearch = () => {
    setQuery('');
    setDebouncedQuery('');
  };

  return {
    query,
    setQuery,
    results,
    loading,
    hasResults: results.length > 0,
    clearSearch,
    executeSearch,
  };
};
