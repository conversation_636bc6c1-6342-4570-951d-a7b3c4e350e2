/**
 * @fileoverview Hook para otimizar touch interactions em mobile
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useCallback, useRef, useState } from 'react';
import { useMobileDetection } from './useMobileDetection';

// ============================================================================
// INTERFACES
// ============================================================================

interface TouchGesture {
  /** Tipo do gesto */
  type: 'tap' | 'swipe' | 'pinch' | 'long-press';
  /** Direção do swipe (se aplicável) */
  direction?: 'left' | 'right' | 'up' | 'down';
  /** Distância do swipe em pixels */
  distance?: number;
  /** Duração do gesto em ms */
  duration?: number;
  /** Posição inicial do touch */
  startPosition?: { x: number; y: number };
  /** Posição final do touch */
  endPosition?: { x: number; y: number };
}

interface TouchInteractionsOptions {
  /** Threshold mínimo para detectar swipe (px) */
  swipeThreshold?: number;
  /** Tempo mínimo para long press (ms) */
  longPressDelay?: number;
  /** Se deve prevenir scroll durante gestos */
  preventScroll?: boolean;
  /** Se deve adicionar feedback haptic */
  enableHaptic?: boolean;
  /** Callbacks para gestos */
  onTap?: (event: TouchEvent) => void;
  onSwipe?: (gesture: TouchGesture) => void;
  onLongPress?: (event: TouchEvent) => void;
  onPinch?: (scale: number) => void;
}

interface TouchInteractionsState {
  /** Se está atualmente tocando */
  isTouching: boolean;
  /** Número de dedos tocando */
  touchCount: number;
  /** Posição atual do touch */
  touchPosition: { x: number; y: number } | null;
  /** Se está fazendo swipe */
  isSwiping: boolean;
  /** Último gesto detectado */
  lastGesture: TouchGesture | null;
}

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Calcula a distância entre dois pontos
 */
const calculateDistance = (
  point1: { x: number; y: number },
  point2: { x: number; y: number }
): number => {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Calcula a direção do swipe
 */
const calculateSwipeDirection = (
  start: { x: number; y: number },
  end: { x: number; y: number }
): 'left' | 'right' | 'up' | 'down' => {
  const dx = end.x - start.x;
  const dy = end.y - start.y;
  
  if (Math.abs(dx) > Math.abs(dy)) {
    return dx > 0 ? 'right' : 'left';
  } else {
    return dy > 0 ? 'down' : 'up';
  }
};

/**
 * Trigger haptic feedback se disponível
 */
const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
  if (typeof window !== 'undefined' && 'vibrate' in navigator) {
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30],
    };
    navigator.vibrate(patterns[type]);
  }
};

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook para otimizar touch interactions em mobile
 * 
 * @example
 * ```tsx
 * const { isTouching, touchPosition, attachTouchHandlers } = useTouchInteractions({
 *   onSwipe: (gesture) => console.log('Swipe:', gesture.direction),
 *   onTap: () => console.log('Tap detected'),
 *   enableHaptic: true
 * });
 * 
 * return (
 *   <div {...attachTouchHandlers()}>
 *     Touch me!
 *   </div>
 * );
 * ```
 */
export const useTouchInteractions = (options: TouchInteractionsOptions = {}) => {
  const {
    swipeThreshold = 50,
    longPressDelay = 500,
    preventScroll = false,
    enableHaptic = false,
    onTap,
    onSwipe,
    onLongPress,
    onPinch,
  } = options;
  
  const { isMobile, hasTouch } = useMobileDetection();
  
  const [state, setState] = useState<TouchInteractionsState>({
    isTouching: false,
    touchCount: 0,
    touchPosition: null,
    isSwiping: false,
    lastGesture: null,
  });
  
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const longPressTimerRef = useRef<number | null>(null);
  const initialDistanceRef = useRef<number | null>(null);
  
  // Handlers de touch
  const handleTouchStart = useCallback((event: TouchEvent) => {
    if (!hasTouch) return;
    
    const touch = event.touches[0];
    const startPosition = { x: touch.clientX, y: touch.clientY };
    const startTime = Date.now();
    
    touchStartRef.current = { ...startPosition, time: startTime };
    
    setState(prev => ({
      ...prev,
      isTouching: true,
      touchCount: event.touches.length,
      touchPosition: startPosition,
      isSwiping: false,
    }));
    
    // Setup long press timer
    if (onLongPress) {
      longPressTimerRef.current = window.setTimeout(() => {
        if (enableHaptic) triggerHaptic('medium');
        onLongPress(event);
      }, longPressDelay);
    }
    
    // Handle pinch start
    if (event.touches.length === 2 && onPinch) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      initialDistanceRef.current = calculateDistance(
        { x: touch1.clientX, y: touch1.clientY },
        { x: touch2.clientX, y: touch2.clientY }
      );
    }
    
    if (preventScroll) {
      event.preventDefault();
    }
  }, [hasTouch, onLongPress, onPinch, longPressDelay, enableHaptic, preventScroll]);
  
  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (!hasTouch || !touchStartRef.current) return;
    
    const touch = event.touches[0];
    const currentPosition = { x: touch.clientX, y: touch.clientY };
    
    setState(prev => ({
      ...prev,
      touchPosition: currentPosition,
      touchCount: event.touches.length,
    }));
    
    // Handle pinch
    if (event.touches.length === 2 && onPinch && initialDistanceRef.current) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      const currentDistance = calculateDistance(
        { x: touch1.clientX, y: touch1.clientY },
        { x: touch2.clientX, y: touch2.clientY }
      );
      const scale = currentDistance / initialDistanceRef.current;
      onPinch(scale);
    }
    
    // Detect swipe
    const distance = calculateDistance(touchStartRef.current, currentPosition);
    if (distance > swipeThreshold) {
      setState(prev => ({ ...prev, isSwiping: true }));
      
      // Clear long press timer
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
        longPressTimerRef.current = null;
      }
    }
    
    if (preventScroll) {
      event.preventDefault();
    }
  }, [hasTouch, onPinch, swipeThreshold, preventScroll]);
  
  const handleTouchEnd = useCallback((event: TouchEvent) => {
    if (!hasTouch || !touchStartRef.current) return;
    
    const endTime = Date.now();
    const duration = endTime - touchStartRef.current.time;
    
    // Clear long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
    
    // Determine gesture type
    if (state.isSwiping && onSwipe) {
      const touch = event.changedTouches[0];
      const endPosition = { x: touch.clientX, y: touch.clientY };
      const distance = calculateDistance(touchStartRef.current, endPosition);
      const direction = calculateSwipeDirection(touchStartRef.current, endPosition);
      
      const gesture: TouchGesture = {
        type: 'swipe',
        direction,
        distance,
        duration,
        startPosition: touchStartRef.current,
        endPosition,
      };
      
      if (enableHaptic) triggerHaptic('light');
      onSwipe(gesture);
      
      setState(prev => ({ ...prev, lastGesture: gesture }));
    } else if (!state.isSwiping && duration < 300 && onTap) {
      // Quick tap
      if (enableHaptic) triggerHaptic('light');
      onTap(event);
      
      const gesture: TouchGesture = {
        type: 'tap',
        duration,
        startPosition: touchStartRef.current,
      };
      
      setState(prev => ({ ...prev, lastGesture: gesture }));
    }
    
    // Reset state
    setState(prev => ({
      ...prev,
      isTouching: false,
      touchCount: 0,
      touchPosition: null,
      isSwiping: false,
    }));
    
    touchStartRef.current = null;
    initialDistanceRef.current = null;
  }, [hasTouch, state.isSwiping, onSwipe, onTap, enableHaptic]);
  
  // Attach touch handlers to element
  const attachTouchHandlers = useCallback(() => {
    if (!isMobile || !hasTouch) return {};
    
    return {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
      style: {
        touchAction: preventScroll ? 'none' : 'auto',
        userSelect: 'none',
        WebkitUserSelect: 'none',
        WebkitTouchCallout: 'none',
      },
    };
  }, [isMobile, hasTouch, handleTouchStart, handleTouchMove, handleTouchEnd, preventScroll]);
  
  return {
    ...state,
    attachTouchHandlers,
    triggerHaptic: enableHaptic ? triggerHaptic : () => {},
  };
};

export default useTouchInteractions;
