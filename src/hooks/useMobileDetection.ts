/**
 * @fileoverview Hook para Detecção Mobile/Tablet/Desktop Responsiva
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useState, useEffect, useCallback } from 'react';

// ============================================================================
// INTERFACES
// ============================================================================

interface MobileDetectionState {
  /** Se é dispositivo mobile (< 768px) */
  isMobile: boolean;
  
  /** Se é tablet (768px - 1024px) */
  isTablet: boolean;
  
  /** Se é desktop (> 1024px) */
  isDesktop: boolean;
  
  /** Se é mobile extra pequeno (< 480px) */
  isMobileXS: boolean;
  
  /** Se é mobile pequeno (480px - 640px) */
  isMobileSM: boolean;
  
  /** Se é mobile grande (640px - 768px) */
  isMobileLG: boolean;
  
  /** Largura atual da tela */
  width: number;
  
  /** Altura atual da tela */
  height: number;
  
  /** Orientação do dispositivo */
  orientation: 'portrait' | 'landscape';
  
  /** Se suporta touch */
  hasTouch: boolean;
  
  /** Device pixel ratio */
  pixelRatio: number;
}

interface UseMobileDetectionOptions {
  /** Debounce delay para resize events (ms) */
  debounceDelay?: number;
  
  /** Se deve detectar mudanças de orientação */
  trackOrientation?: boolean;
  
  /** Se deve detectar capacidades touch */
  detectTouch?: boolean;
  
  /** Breakpoints customizados */
  breakpoints?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

const DEFAULT_BREAKPOINTS = {
  mobile: 768,   // < 768px = mobile
  tablet: 1024,  // 768px - 1024px = tablet
  desktop: 1024, // > 1024px = desktop
};

const MOBILE_BREAKPOINTS = {
  xs: 480,  // < 480px = mobile-xs
  sm: 640,  // 480px - 640px = mobile-sm
  lg: 768,  // 640px - 768px = mobile-lg
};

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Debounce function para otimizar performance
 */
const debounce = <T extends (...args: any[]) => void>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Detecta se o dispositivo suporta touch
 */
const detectTouchSupport = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    // @ts-ignore
    navigator.msMaxTouchPoints > 0
  );
};

/**
 * Calcula o estado baseado na largura da tela
 */
const calculateState = (
  width: number,
  height: number,
  breakpoints: typeof DEFAULT_BREAKPOINTS,
  options: UseMobileDetectionOptions
): MobileDetectionState => {
  const isMobile = width < breakpoints.mobile;
  const isTablet = width >= breakpoints.mobile && width < breakpoints.desktop;
  const isDesktop = width >= breakpoints.desktop;
  
  const isMobileXS = width < MOBILE_BREAKPOINTS.xs;
  const isMobileSM = width >= MOBILE_BREAKPOINTS.xs && width < MOBILE_BREAKPOINTS.sm;
  const isMobileLG = width >= MOBILE_BREAKPOINTS.sm && width < MOBILE_BREAKPOINTS.lg;
  
  const orientation = width > height ? 'landscape' : 'portrait';
  const hasTouch = options.detectTouch ? detectTouchSupport() : false;
  const pixelRatio = typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1;
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    isMobileXS,
    isMobileSM,
    isMobileLG,
    width,
    height,
    orientation,
    hasTouch,
    pixelRatio,
  };
};

/**
 * Obtém dimensões iniciais da tela (SSR-safe)
 */
const getInitialDimensions = () => {
  if (typeof window === 'undefined') {
    // SSR fallback - assume desktop
    return { width: 1024, height: 768 };
  }
  
  return {
    width: window.innerWidth,
    height: window.innerHeight,
  };
};

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook para detecção responsiva de dispositivos mobile/tablet/desktop
 * 
 * @example
 * ```tsx
 * const { isMobile, isTablet, isDesktop, width } = useMobileDetection();
 * 
 * return (
 *   <div>
 *     {isMobile && <MobileComponent />}
 *     {isTablet && <TabletComponent />}
 *     {isDesktop && <DesktopComponent />}
 *   </div>
 * );
 * ```
 */
export const useMobileDetection = (options: UseMobileDetectionOptions = {}): MobileDetectionState => {
  const {
    debounceDelay = 150,
    trackOrientation = true,
    detectTouch: _detectTouch = true,
    breakpoints = DEFAULT_BREAKPOINTS,
  } = options;

  // Estado inicial (SSR-safe)
  const initialDimensions = getInitialDimensions();
  const [state, setState] = useState<MobileDetectionState>(() =>
    calculateState(initialDimensions.width, initialDimensions.height, breakpoints, options)
  );

  // Handler para resize events
  const handleResize = useCallback(() => {
    if (typeof window === 'undefined') return;
    
    const newWidth = window.innerWidth;
    const newHeight = window.innerHeight;
    
    setState(calculateState(newWidth, newHeight, breakpoints, options));
  }, [breakpoints, options]);

  // Handler para orientation change
  const handleOrientationChange = useCallback(() => {
    if (!trackOrientation || typeof window === 'undefined') return;
    
    // Aguardar um pouco para as dimensões se atualizarem
    setTimeout(() => {
      handleResize();
    }, 100);
  }, [handleResize, trackOrientation]);

  // Debounced handlers
  const debouncedResize = useCallback(
    debounce(handleResize, debounceDelay),
    [handleResize, debounceDelay]
  );

  // Effect para setup dos listeners
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Atualizar estado inicial no cliente
    handleResize();

    // Adicionar listeners
    window.addEventListener('resize', debouncedResize, { passive: true });
    
    if (trackOrientation) {
      window.addEventListener('orientationchange', handleOrientationChange, { passive: true });
    }

    // Cleanup
    return () => {
      window.removeEventListener('resize', debouncedResize);
      
      if (trackOrientation) {
        window.removeEventListener('orientationchange', handleOrientationChange);
      }
    };
  }, [debouncedResize, handleOrientationChange, trackOrientation, handleResize]);

  return state;
};

// ============================================================================
// HOOKS ESPECIALIZADOS
// ============================================================================

/**
 * Hook simplificado que retorna apenas se é mobile
 */
export const useIsMobile = (breakpoint: number = DEFAULT_BREAKPOINTS.mobile): boolean => {
  const { isMobile } = useMobileDetection({ breakpoints: { ...DEFAULT_BREAKPOINTS, mobile: breakpoint } });
  return isMobile;
};

/**
 * Hook para detectar mudanças de orientação
 */
export const useOrientation = (): 'portrait' | 'landscape' => {
  const { orientation } = useMobileDetection({ trackOrientation: true });
  return orientation;
};

/**
 * Hook para detectar capacidades touch
 */
export const useHasTouch = (): boolean => {
  const { hasTouch } = useMobileDetection({ detectTouch: true });
  return hasTouch;
};

/**
 * Hook para obter breakpoint atual
 */
export const useCurrentBreakpoint = (): 'mobile-xs' | 'mobile-sm' | 'mobile-lg' | 'tablet' | 'desktop' => {
  const { isMobileXS, isMobileSM, isMobileLG, isTablet } = useMobileDetection();

  if (isMobileXS) return 'mobile-xs';
  if (isMobileSM) return 'mobile-sm';
  if (isMobileLG) return 'mobile-lg';
  if (isTablet) return 'tablet';
  return 'desktop';
};

export default useMobileDetection;
