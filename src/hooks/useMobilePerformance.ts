/**
 * @fileoverview Hook para otimizações de performance mobile
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useEffect, useState } from 'react';
import { useMobileDetection } from './useMobileDetection';

// ============================================================================
// INTERFACES
// ============================================================================

interface MobilePerformanceState {
  /** Se deve reduzir animações */
  reduceAnimations: boolean;
  /** Se deve usar lazy loading agressivo */
  aggressiveLazyLoading: boolean;
  /** Se deve reduzir qualidade de imagens */
  reduceImageQuality: boolean;
  /** Se deve desabilitar efeitos visuais */
  disableEffects: boolean;
  /** Nível de performance do dispositivo */
  performanceLevel: 'low' | 'medium' | 'high';
}

interface MobilePerformanceOptions {
  /** Detectar automaticamente performance do dispositivo */
  autoDetectPerformance?: boolean;
  /** Forçar modo de baixa performance */
  forceLowPerformance?: boolean;
  /** Threshold para considerar dispositivo lento (ms) */
  slowDeviceThreshold?: number;
}

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Detecta a performance do dispositivo baseado em métricas
 */
const detectDevicePerformance = (): 'low' | 'medium' | 'high' => {
  if (typeof window === 'undefined') return 'medium';
  
  const navigator = window.navigator as any;
  
  // Verificar hardware concurrency (número de cores)
  const cores = navigator.hardwareConcurrency || 2;
  
  // Verificar memória disponível (se suportado)
  const memory = navigator.deviceMemory || 4;
  
  // Verificar connection type (se suportado)
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  const effectiveType = connection?.effectiveType || '4g';
  
  // Calcular score baseado nas métricas
  let score = 0;
  
  // Score baseado em cores
  if (cores >= 8) score += 3;
  else if (cores >= 4) score += 2;
  else score += 1;
  
  // Score baseado em memória
  if (memory >= 8) score += 3;
  else if (memory >= 4) score += 2;
  else score += 1;
  
  // Score baseado em conexão
  if (effectiveType === '4g') score += 2;
  else if (effectiveType === '3g') score += 1;
  else score += 0;
  
  // Determinar nível de performance
  if (score >= 7) return 'high';
  if (score >= 4) return 'medium';
  return 'low';
};

/**
 * Mede o tempo de renderização para detectar dispositivos lentos
 */
const measureRenderTime = (): Promise<number> => {
  return new Promise((resolve) => {
    const start = performance.now();
    
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        const end = performance.now();
        resolve(end - start);
      });
    });
  });
};

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook para otimizações de performance mobile
 * 
 * @example
 * ```tsx
 * const { reduceAnimations, aggressiveLazyLoading, performanceLevel } = useMobilePerformance();
 * 
 * return (
 *   <div className={reduceAnimations ? 'no-animations' : 'with-animations'}>
 *     {aggressiveLazyLoading ? <LazyComponent /> : <Component />}
 *   </div>
 * );
 * ```
 */
export const useMobilePerformance = (options: MobilePerformanceOptions = {}): MobilePerformanceState => {
  const {
    autoDetectPerformance = true,
    forceLowPerformance = false,
    slowDeviceThreshold = 32, // ms
  } = options;
  
  const { isMobile, isMobileXS } = useMobileDetection();
  const [performanceLevel, setPerformanceLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const [isSlowDevice, setIsSlowDevice] = useState(false);
  
  // Detectar performance do dispositivo
  useEffect(() => {
    if (!autoDetectPerformance || typeof window === 'undefined') return;
    
    const detectPerformance = async () => {
      try {
        // Detectar baseado em hardware
        const hardwareLevel = detectDevicePerformance();
        
        // Medir tempo de renderização
        const renderTime = await measureRenderTime();
        const isSlow = renderTime > slowDeviceThreshold;
        
        setIsSlowDevice(isSlow);
        
        // Ajustar nível baseado em medições
        let finalLevel = hardwareLevel;
        if (isSlow && finalLevel === 'high') finalLevel = 'medium';
        if (isSlow && finalLevel === 'medium') finalLevel = 'low';
        
        setPerformanceLevel(finalLevel);
      } catch (error) {
        console.warn('Erro ao detectar performance do dispositivo:', error);
        setPerformanceLevel('medium');
      }
    };
    
    detectPerformance();
  }, [autoDetectPerformance, slowDeviceThreshold]);
  
  // Calcular otimizações baseado no estado
  const finalPerformanceLevel = forceLowPerformance ? 'low' : performanceLevel;
  
  const reduceAnimations = isMobile && (finalPerformanceLevel === 'low' || isSlowDevice);
  const aggressiveLazyLoading = isMobile && finalPerformanceLevel === 'low';
  const reduceImageQuality = isMobileXS || finalPerformanceLevel === 'low';
  const disableEffects = isMobile && finalPerformanceLevel === 'low';
  
  return {
    reduceAnimations,
    aggressiveLazyLoading,
    reduceImageQuality,
    disableEffects,
    performanceLevel: finalPerformanceLevel,
  };
};

// ============================================================================
// HOOKS ESPECIALIZADOS
// ============================================================================

/**
 * Hook para detectar se deve reduzir animações
 */
export const useReduceAnimations = (): boolean => {
  const { reduceAnimations } = useMobilePerformance();
  return reduceAnimations;
};

/**
 * Hook para detectar se deve usar lazy loading agressivo
 */
export const useAggressiveLazyLoading = (): boolean => {
  const { aggressiveLazyLoading } = useMobilePerformance();
  return aggressiveLazyLoading;
};

/**
 * Hook para detectar nível de performance
 */
export const usePerformanceLevel = (): 'low' | 'medium' | 'high' => {
  const { performanceLevel } = useMobilePerformance();
  return performanceLevel;
};

export default useMobilePerformance;
