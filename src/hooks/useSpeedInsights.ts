/**
 * @fileoverview Hook para Vercel Speed Insights - Métricas de Performance
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// ============================================================================
// INTERFACES
// ============================================================================

interface PerformanceMetrics {
  /**
   * First Contentful Paint - Tempo até o primeiro conteúdo aparecer
   */
  fcp?: number;
  
  /**
   * Largest Contentful Paint - Tempo até o maior elemento aparecer
   */
  lcp?: number;
  
  /**
   * First Input Delay - Tempo de resposta à primeira interação
   */
  fid?: number;
  
  /**
   * Cumulative Layout Shift - Mudanças inesperadas de layout
   */
  cls?: number;
  
  /**
   * Time to First Byte - Tempo até o primeiro byte do servidor
   */
  ttfb?: number;
  
  /**
   * Interaction to Next Paint - Responsividade das interações
   */
  inp?: number;
}

interface SpeedInsightsEvent {
  name: string;
  value?: number;
  rating?: 'good' | 'needs-improvement' | 'poor';
  delta?: number;
  id?: string;
  navigationType?: 'navigate' | 'reload' | 'back-forward' | 'prerender';
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

/**
 * Thresholds para classificação das métricas (baseado no Core Web Vitals)
 */
const PERFORMANCE_THRESHOLDS = {
  fcp: { good: 1800, poor: 3000 },
  lcp: { good: 2500, poor: 4000 },
  fid: { good: 100, poor: 300 },
  cls: { good: 0.1, poor: 0.25 },
  ttfb: { good: 800, poor: 1800 },
  inp: { good: 200, poor: 500 },
} as const;

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Classifica uma métrica baseada nos thresholds
 */
const getMetricRating = (
  metricName: keyof typeof PERFORMANCE_THRESHOLDS,
  value: number
): 'good' | 'needs-improvement' | 'poor' => {
  const thresholds = PERFORMANCE_THRESHOLDS[metricName];
  
  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
};

/**
 * Formata valor de métrica para exibição
 */
const formatMetricValue = (metricName: string, value: number): string => {
  switch (metricName) {
    case 'cls':
      return value.toFixed(3);
    case 'fcp':
    case 'lcp':
    case 'fid':
    case 'ttfb':
    case 'inp':
      return `${Math.round(value)}ms`;
    default:
      return value.toString();
  }
};

/**
 * Log de métricas para desenvolvimento
 */
const logMetric = (event: SpeedInsightsEvent) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 [Speed Insights]', {
      metric: event.name,
      value: event.value ? formatMetricValue(event.name, event.value) : 'N/A',
      rating: event.rating,
      delta: event.delta,
      navigationType: event.navigationType,
    });
  }
};

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

/**
 * Hook para monitoramento de métricas de performance com Vercel Speed Insights
 */
export const useSpeedInsights = () => {
  const location = useLocation();

  /**
   * Inicializar observadores de performance
   */
  useEffect(() => {
    // Só executar em produção ou quando explicitamente habilitado
    if (process.env.NODE_ENV !== 'production' && !process.env.VITE_ENABLE_SPEED_INSIGHTS) {
      return;
    }

    // Observer para Web Vitals
    const observeWebVitals = () => {
      // FCP - First Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const fcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
            
            if (fcpEntry) {
              const event: SpeedInsightsEvent = {
                name: 'FCP',
                value: fcpEntry.startTime,
                rating: getMetricRating('fcp', fcpEntry.startTime),
                navigationType: (performance.navigation as any)?.type === 1 ? 'reload' : 'navigate',
              };
              
              logMetric(event);
            }
          });
          
          fcpObserver.observe({ entryTypes: ['paint'] });
        } catch (error) {
          console.warn('Speed Insights: FCP observer failed', error);
        }

        // LCP - Largest Contentful Paint
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            
            if (lastEntry) {
              const event: SpeedInsightsEvent = {
                name: 'LCP',
                value: lastEntry.startTime,
                rating: getMetricRating('lcp', lastEntry.startTime),
                navigationType: (performance.navigation as any)?.type === 1 ? 'reload' : 'navigate',
              };
              
              logMetric(event);
            }
          });
          
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (error) {
          console.warn('Speed Insights: LCP observer failed', error);
        }

        // CLS - Cumulative Layout Shift
        try {
          let clsValue = 0;
          const clsObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            
            const event: SpeedInsightsEvent = {
              name: 'CLS',
              value: clsValue,
              rating: getMetricRating('cls', clsValue),
            };
            
            logMetric(event);
          });
          
          clsObserver.observe({ entryTypes: ['layout-shift'] });
        } catch (error) {
          console.warn('Speed Insights: CLS observer failed', error);
        }
      }
    };

    // Inicializar observadores
    observeWebVitals();

    // Cleanup não necessário pois os observers são automaticamente limpos
  }, []);

  /**
   * Track page view para Speed Insights
   */
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      // O SpeedInsights automaticamente tracka page views
      // Mas podemos adicionar logs customizados
      console.log('📊 [Speed Insights] Page view:', location.pathname);
    }
  }, [location.pathname]);

  /**
   * Função para track de eventos customizados
   */
  const trackCustomEvent = (eventName: string, value?: number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 [Speed Insights] Custom event:', eventName, value);
    }
    
    // Em produção, o Vercel Speed Insights captura automaticamente
    // Mas podemos adicionar métricas customizadas se necessário
  };

  /**
   * Função para obter métricas atuais
   */
  const getCurrentMetrics = (): PerformanceMetrics => {
    const metrics: PerformanceMetrics = {};

    if ('performance' in window && 'getEntriesByType' in performance) {
      // FCP
      const paintEntries = performance.getEntriesByType('paint');
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
      }

      // Navigation timing para TTFB
      const navEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
      if (navEntries.length > 0) {
        const navEntry = navEntries[0];
        metrics.ttfb = navEntry.responseStart - navEntry.requestStart;
      }
    }

    return metrics;
  };

  return {
    trackCustomEvent,
    getCurrentMetrics,
    formatMetricValue,
    getMetricRating,
  };
};

export default useSpeedInsights;
