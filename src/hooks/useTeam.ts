/**
 * @fileoverview Hook para gerenciar dados da equipe
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabaseClient';

// ============================================================================
// INTERFACES
// ============================================================================

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar: string;
  bio: string;
  role: string;
  postsCount: number;
  lastPostDate?: string;
  social: {
    github?: string;
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
}

interface UseTeamReturn {
  teamMembers: TeamMember[];
  loading: boolean;
  error: string | null;
  refreshTeam: () => Promise<void>;
}

// ============================================================================
// HOOK
// ============================================================================

/**
 * Hook para buscar dados da equipe baseado nos autores dos posts
 */
export const useTeam = (): UseTeamReturn => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTeamMembers = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      // Buscar todos os profiles que são autores de posts
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          name,
          email,
          avatar,
          bio,
          role,
          social_links,
          created_at,
          updated_at
        `)
        .order('name', { ascending: true });

      if (profilesError) {
        throw new Error(profilesError.message || 'Erro ao buscar perfis');
      }

      // Para cada profile, buscar estatísticas de posts
      const teamMembersWithStats = await Promise.all(
        (profilesData || []).map(async (profile) => {
          // Contar posts do autor
          const { count: postsCount } = await supabase
            .from('posts')
            .select('*', { count: 'exact', head: true })
            .eq('author_id', profile.id)
            .eq('status', 'published');

          // Buscar último post
          const { data: lastPost } = await supabase
            .from('posts')
            .select('published_at')
            .eq('author_id', profile.id)
            .eq('status', 'published')
            .order('published_at', { ascending: false })
            .limit(1)
            .single();

          // Processar social links
          let socialLinks: any = {};
          try {
            socialLinks = profile.social_links ? JSON.parse(profile.social_links) : {};
          } catch {
            socialLinks = {};
          }

          const teamMember: TeamMember = {
            id: profile.id,
            name: profile.name || 'Membro da Equipe',
            email: profile.email || '',
            avatar: profile.avatar || '',
            bio: profile.bio || 'Membro da equipe Blueprint Blog',
            role: profile.role || 'Colaborador',
            postsCount: postsCount || 0,
            lastPostDate: lastPost?.published_at || undefined,
            social: {
              github: socialLinks.github || undefined,
              linkedin: socialLinks.linkedin || undefined,
              twitter: socialLinks.twitter || undefined,
              website: socialLinks.website || undefined,
            }
          };

          return teamMember;
        })
      );

      // Filtrar apenas membros com posts ou com role específico
      const activeMembers = teamMembersWithStats.filter(
        member => member.postsCount > 0 || member.role === 'admin'
      );

      setTeamMembers(activeMembers);
    } catch (err) {
      console.error('Erro ao buscar equipe:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      
      // Fallback para dados mockados se houver erro
      setTeamMembers([
        {
          id: '1',
          name: 'Blueprint Team',
          email: '<EMAIL>',
          avatar: '',
          bio: 'Equipe dedicada ao desenvolvimento e manutenção do Blueprint Blog v2.',
          role: 'admin',
          postsCount: 0,
          social: {
            github: 'https://github.com/blueprintblog',
          }
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeamMembers();
  }, []);

  return {
    teamMembers,
    loading,
    error,
    refreshTeam: fetchTeamMembers,
  };
};
