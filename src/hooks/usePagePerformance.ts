/**
 * @fileoverview Hook para monitoramento de performance das páginas
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useEffect, useRef } from 'react';
import { useAnalytics } from './useAnalytics';

// ============================================================================
// INTERFACES
// ============================================================================

interface PagePerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage?: number;
}

interface UsePagePerformanceOptions {
  pageName: string;
  trackLoadTime?: boolean;
  trackRenderTime?: boolean;
  trackInteractions?: boolean;
  trackMemory?: boolean;
}

// ============================================================================
// HOOK
// ============================================================================

/**
 * Hook para monitorar performance de páginas específicas
 */
export const usePagePerformance = (options: UsePagePerformanceOptions) => {
  const { trackEvent } = useAnalytics();
  const startTimeRef = useRef<number>(Date.now());
  const renderTimeRef = useRef<number | null>(null);
  const interactionCountRef = useRef<number>(0);

  const {
    pageName,
    trackLoadTime = true,
    trackRenderTime = true,
    trackInteractions = true,
    trackMemory = false
  } = options;

  // Medir tempo de carregamento inicial
  useEffect(() => {
    if (!trackLoadTime) return;

    const measureLoadTime = () => {
      const loadTime = Date.now() - startTimeRef.current;
      
      trackEvent('page_load_time', {
        page_name: pageName,
        load_time: loadTime,
        event_category: 'Performance'
      });

      // Medir Web Vitals específicos da página
      if (typeof window !== 'undefined' && 'performance' in window) {
        // Largest Contentful Paint
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          
          if (lastEntry) {
            trackEvent('lcp_measurement', {
              page_name: pageName,
              lcp_time: Math.round(lastEntry.startTime),
              event_category: 'Web Vitals'
            });
          }
        });

        try {
          observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (error) {
          console.warn('LCP observation not supported:', error);
        }

        // Cleanup
        return () => {
          observer.disconnect();
        };
      }
    };

    // Aguardar carregamento completo
    if (document.readyState === 'complete') {
      measureLoadTime();
    } else {
      window.addEventListener('load', measureLoadTime);
      return () => window.removeEventListener('load', measureLoadTime);
    }
  }, [pageName, trackLoadTime, trackEvent]);

  // Medir tempo de renderização
  useEffect(() => {
    if (!trackRenderTime) return;

    renderTimeRef.current = Date.now();
    
    // Usar requestAnimationFrame para medir após renderização
    const measureRenderTime = () => {
      if (renderTimeRef.current) {
        const renderTime = Date.now() - renderTimeRef.current;
        
        trackEvent('page_render_time', {
          page_name: pageName,
          render_time: renderTime,
          event_category: 'Performance'
        });
      }
    };

    requestAnimationFrame(measureRenderTime);
  }, [pageName, trackRenderTime, trackEvent]);

  // Monitorar interações do usuário
  useEffect(() => {
    if (!trackInteractions) return;

    const trackInteraction = (eventType: string) => {
      interactionCountRef.current++;
      
      // Track apenas a primeira interação para TTI
      if (interactionCountRef.current === 1) {
        const timeToInteraction = Date.now() - startTimeRef.current;
        
        trackEvent('time_to_interaction', {
          page_name: pageName,
          tti_time: timeToInteraction,
          first_interaction: eventType,
          event_category: 'Performance'
        });
      }
    };

    const handleClick = () => trackInteraction('click');
    const handleKeydown = () => trackInteraction('keydown');
    const handleScroll = () => trackInteraction('scroll');

    document.addEventListener('click', handleClick, { once: true });
    document.addEventListener('keydown', handleKeydown, { once: true });
    document.addEventListener('scroll', handleScroll, { once: true });

    return () => {
      document.removeEventListener('click', handleClick);
      document.removeEventListener('keydown', handleKeydown);
      document.removeEventListener('scroll', handleScroll);
    };
  }, [pageName, trackInteractions, trackEvent]);

  // Monitorar uso de memória
  useEffect(() => {
    if (!trackMemory || typeof window === 'undefined') return;

    const measureMemory = () => {
      // @ts-ignore - performance.memory pode não estar disponível em todos os browsers
      if (performance.memory) {
        // @ts-ignore
        const memoryInfo = performance.memory;
        
        trackEvent('memory_usage', {
          page_name: pageName,
          used_js_heap_size: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024), // MB
          total_js_heap_size: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024), // MB
          js_heap_size_limit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024), // MB
          event_category: 'Performance'
        });
      }
    };

    // Medir memória após 5 segundos
    const timeoutId = setTimeout(measureMemory, 5000);
    
    return () => clearTimeout(timeoutId);
  }, [pageName, trackMemory, trackEvent]);

  // Função para medir performance customizada
  const measureCustomMetric = (metricName: string, value: number, unit: string = 'ms') => {
    trackEvent('custom_performance_metric', {
      page_name: pageName,
      metric_name: metricName,
      metric_value: value,
      metric_unit: unit,
      event_category: 'Performance'
    });
  };

  // Função para marcar início de operação
  const markStart = (operationName: string) => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(`${pageName}-${operationName}-start`);
    }
  };

  // Função para marcar fim de operação e medir
  const markEnd = (operationName: string) => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const endMark = `${pageName}-${operationName}-end`;
      const startMark = `${pageName}-${operationName}-start`;
      
      performance.mark(endMark);
      
      try {
        performance.measure(`${pageName}-${operationName}`, startMark, endMark);
        
        const measure = performance.getEntriesByName(`${pageName}-${operationName}`)[0];
        if (measure) {
          trackEvent('operation_duration', {
            page_name: pageName,
            operation_name: operationName,
            duration: Math.round(measure.duration),
            event_category: 'Performance'
          });
        }
      } catch (error) {
        console.warn('Performance measurement failed:', error);
      }
    }
  };

  return {
    measureCustomMetric,
    markStart,
    markEnd,
    getMetrics: (): PagePerformanceMetrics => ({
      loadTime: Date.now() - startTimeRef.current,
      renderTime: renderTimeRef.current ? Date.now() - renderTimeRef.current : 0,
      interactionTime: interactionCountRef.current > 0 ? Date.now() - startTimeRef.current : 0,
      // @ts-ignore
      memoryUsage: typeof window !== 'undefined' && performance.memory 
        // @ts-ignore
        ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) 
        : undefined
    })
  };
};
