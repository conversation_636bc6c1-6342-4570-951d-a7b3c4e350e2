/**
 * @fileoverview Hook para gerenciar logs de debug - Fase 3 do Sistema de Logging
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { logger, LogLevel, type LogEntry } from '../utils/logger';

// ============================================================================
// INTERFACES
// ============================================================================

interface LogFilters {
  level?: LogLevel;
  search?: string;
  timeRange?: 'all' | '1h' | '24h' | '7d';
  component?: string;
}

interface LogStats {
  total: number;
  errors: number;
  warnings: number;
  info: number;
  debug: number;
}

interface UseDebugLogsReturn {
  logs: LogEntry[];
  filteredLogs: LogEntry[];
  stats: LogStats;
  filters: LogFilters;
  loading: boolean;
  setFilters: (filters: LogFilters) => void;
  clearFilters: () => void;
  refreshLogs: () => void;
  exportLogs: () => void;
  clearLogs: () => void;
}

// ============================================================================
// HOOK
// ============================================================================

/**
 * Hook para gerenciar logs de debug
 */
export const useDebugLogs = (
  autoRefresh: boolean = true
): UseDebugLogsReturn => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filters, setFilters] = useState<LogFilters>({});
  const [loading, setLoading] = useState(true);
  const isProduction = process.env.NODE_ENV === 'production';

  // Carregar logs do logger
  const refreshLogs = useCallback(() => {
    // Em produção, não carregar logs para economizar performance
    if (isProduction) {
      setLogs([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const allLogs = logger.getLogs();
      setLogs(allLogs);
    } catch (error) {
      console.error('Erro ao carregar logs:', error);
    } finally {
      setLoading(false);
    }
  }, [isProduction]);

  // Auto-refresh dos logs (desabilitado em produção)
  useEffect(() => {
    refreshLogs();

    if (autoRefresh && !isProduction) {
      // Reduzir frequência de refresh para 5 segundos em desenvolvimento
      const interval = setInterval(refreshLogs, 5000);
      return () => clearInterval(interval);
    }
  }, [refreshLogs, autoRefresh, isProduction]);

  // Filtrar logs
  const filteredLogs = useCallback(() => {
    let filtered = [...logs];

    // Filtro por nível
    if (filters.level !== undefined) {
      filtered = filtered.filter((log) => log.level >= filters.level!);
    }

    // Filtro por busca
    if (filters.search) {
      const search = filters.search.toLowerCase();
      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(search) ||
          (log.context?.component &&
            log.context.component.toLowerCase().includes(search)) ||
          (log.context?.type && log.context.type.toLowerCase().includes(search))
      );
    }

    // Filtro por componente
    if (filters.component) {
      filtered = filtered.filter(
        (log) => log.context?.component === filters.component
      );
    }

    // Filtro por tempo
    if (filters.timeRange && filters.timeRange !== 'all') {
      const now = new Date();
      const timeRanges = {
        '1h': 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
      };

      const cutoff = new Date(now.getTime() - timeRanges[filters.timeRange]);
      filtered = filtered.filter((log) => log.timestamp >= cutoff);
    }

    return filtered.reverse(); // Mais recentes primeiro
  }, [logs, filters]);

  // Calcular estatísticas
  const stats = useCallback((): LogStats => {
    const filtered = filteredLogs();
    const total = filtered.length;
    const errors = filtered.filter(
      (log) => log.level === LogLevel.ERROR
    ).length;
    const warnings = filtered.filter(
      (log) => log.level === LogLevel.WARN
    ).length;
    const info = filtered.filter((log) => log.level === LogLevel.INFO).length;
    const debug = filtered.filter((log) => log.level === LogLevel.DEBUG).length;

    return { total, errors, warnings, info, debug };
  }, [filteredLogs]);

  // Limpar filtros
  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  // Exportar logs
  const exportLogs = useCallback(() => {
    try {
      const logsToExport = filteredLogs();
      const dataStr = JSON.stringify(logsToExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `debug-logs-${
        new Date().toISOString().split('T')[0]
      }.json`;
      link.click();

      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar logs:', error);
    }
  }, [filteredLogs]);

  // Limpar logs
  const clearLogs = useCallback(() => {
    if (confirm('Tem certeza que deseja limpar todos os logs?')) {
      logger.clearLogs();
      setLogs([]);
    }
  }, []);

  return {
    logs,
    filteredLogs: filteredLogs(),
    stats: stats(),
    filters,
    loading,
    setFilters,
    clearFilters,
    refreshLogs,
    exportLogs,
    clearLogs,
  };
};

// ============================================================================
// HOOK PARA COMPONENTES ESPECÍFICOS
// ============================================================================

/**
 * Hook para logs de um componente específico
 */
export const useComponentLogs = (componentName: string): UseDebugLogsReturn => {
  const debugLogs = useDebugLogs(true);
  const initializedRef = useRef(false);

  // Filtrar automaticamente por componente apenas uma vez
  useEffect(() => {
    if (!initializedRef.current) {
      debugLogs.setFilters({ component: componentName });
      initializedRef.current = true;
    }
  }, [componentName, debugLogs.setFilters]);

  return debugLogs;
};

// ============================================================================
// HOOK PARA MONITORAMENTO DE ERROS
// ============================================================================

/**
 * Hook para monitorar apenas erros
 */
export const useErrorLogs = (): UseDebugLogsReturn => {
  const debugLogs = useDebugLogs(true);
  const initializedRef = useRef(false);

  // Filtrar automaticamente por erros apenas uma vez
  useEffect(() => {
    if (!initializedRef.current) {
      debugLogs.setFilters({ level: LogLevel.ERROR });
      initializedRef.current = true;
    }
  }, [debugLogs.setFilters]);

  return debugLogs;
};

// ============================================================================
// HOOK PARA ESTATÍSTICAS EM TEMPO REAL
// ============================================================================

/**
 * Hook para estatísticas de logs em tempo real
 */
export const useLogStats = () => {
  const [stats, setStats] = useState<LogStats>({
    total: 0,
    errors: 0,
    warnings: 0,
    info: 0,
    debug: 0,
  });
  const isProduction = process.env.NODE_ENV === 'production';

  useEffect(() => {
    // Em produção, não calcular stats para economizar performance
    if (isProduction) {
      return;
    }

    const updateStats = () => {
      const logs = logger.getLogs();
      const newStats = {
        total: logs.length,
        errors: logs.filter((log) => log.level === LogLevel.ERROR).length,
        warnings: logs.filter((log) => log.level === LogLevel.WARN).length,
        info: logs.filter((log) => log.level === LogLevel.INFO).length,
        debug: logs.filter((log) => log.level === LogLevel.DEBUG).length,
      };
      setStats(newStats);
    };

    updateStats();
    // Reduzir frequência de atualização para 5 segundos
    const interval = setInterval(updateStats, 5000);

    return () => clearInterval(interval);
  }, [isProduction]);

  return stats;
};

export default useDebugLogs;
