/**
 * @fileoverview Componente principal da aplicação Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { Analytics } from '@vercel/analytics/react';
import { lazy, Suspense } from 'react';
import { HelmetProvider } from 'react-helmet-async';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router';
import ProtectedRoute from './components/auth/ProtectedRoute';
import { ResourcePreloader } from './components/optimization/ResourcePreloader';
// Importar utilitários de promoção (disponível no console)
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';
import { useAnalytics } from './hooks/useAnalytics';
import {
  About,
  ApiDocs,
  Archive,
  Blog,
  Careers,
  Contact,
  Docs,
  Home,
  PostPage,
  Status,
  Support,
  Team
} from './pages';
import { LGPD, Terms, Privacy, Cookies } from './pages/legal';
import { Login } from './pages/auth/Login';
import './utils/promoteUser';

// Componente wrapper para analytics
const AnalyticsWrapper: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // Inicializar Google Analytics tracking
  useAnalytics();

  return <>{children}</>;
};

// Lazy loading para páginas menos usadas
const CategoryPage = lazy(() =>
  import('./pages/Category').then((m) => ({ default: m.CategoryPage }))
);
const TagPage = lazy(() =>
  import('./pages/Tag').then((m) => ({ default: m.TagPage }))
);
const MainLayout = lazy(() =>
  import('./components/layout/MainLayout').then((m) => ({
    default: m.MainLayout,
  }))
);

const Profile = lazy(() =>
  import('./pages/Profile').then((m) => ({ default: m.Profile }))
);
const AdminSettings = lazy(() =>
  import('./pages/admin/Settings').then((m) => ({ default: m.Settings }))
);
const AdminDashboard = lazy(() =>
  import('./pages/admin/AdminDashboard').then((m) => ({
    default: m.AdminDashboard,
  }))
);
const UsersManager = lazy(() =>
  import('./pages/admin/UsersManager').then((m) => ({
    default: m.UsersManager,
  }))
);
const PostsList = lazy(() =>
  import('./pages/admin/PostsList').then((m) => ({ default: m.PostsList }))
);
const PostEditor = lazy(() => import('./pages/admin/PostEditor'));
const CategoriesManager = lazy(() =>
  import('./pages/admin/CategoriesManager').then((m) => ({
    default: m.CategoriesManager,
  }))
);
const TagsManager = lazy(() =>
  import('./pages/admin/TagsManager').then((m) => ({ default: m.TagsManager }))
);
const AdminAnalytics = lazy(() =>
  import('./pages/admin/Analytics').then((m) => ({ default: m.Analytics }))
);
const DebugPage = lazy(() =>
  import('./pages/admin/debug').then((m) => ({ default: m.DebugPage }))
);
const MediaManager = lazy(() =>
  import('./pages/admin/MediaManager').then((m) => ({
    default: m.MediaManager,
  }))
);
const DashboardLayout = lazy(() =>
  import('./components/layout/DashboardLayout').then((m) => ({
    default: m.DashboardLayout,
  }))
);

// Loading Component otimizado
const PageLoader = () => (
  <div className="flex justify-center items-center py-8 min-h-[200px]">
    <div className="relative">
      <div className="w-12 h-12 border-4 border-neon-cyan/30 border-t-neon-cyan rounded-full animate-spin"></div>
      <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-neon-magenta rounded-full animate-spin animate-reverse"></div>
    </div>
  </div>
);

/**
 * Componente principal da aplicação com sistema de roteamento
 */
function App() {
  return (
    <HelmetProvider>
      <Router>
        <AuthProvider>
          <ToastProvider>
            <AnalyticsWrapper>
              {/* Preloader de recursos críticos */}
              <ResourcePreloader
                resources={
                  [
                    // Fonts críticas (se houver)
                    // '/fonts/inter.woff2',
                  ]
                }
                components={[
                  () => import('./components/layout/MainLayout'),
                  () => import('./pages/Post'),
                ]}
              />
              <Routes>
                {/* Página inicial */}
                <Route
                  path="/"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Home />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Blog */}
                <Route
                  path="/blog"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Blog />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Post individual */}
                <Route
                  path="/post/:slug"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <PostPage />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Categorias */}
                <Route
                  path="/category/:slug"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <CategoryPage />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Tags */}
                <Route
                  path="/tag/:slug"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <TagPage />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Autenticação */}
                <Route path="/login" element={<Login />} />

                {/* Redirect dashboard antigo para novo */}
                <Route
                  path="/dashboard"
                  element={<Navigate to="/admin" replace />}
                />

                {/* Profile (protegido) */}
                <Route
                  path="/profile"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <MainLayout>
                          <Profile />
                        </MainLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Admin Routes */}
                {/* Admin Dashboard Principal */}
                <Route
                  path="/admin"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <AdminDashboard />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Posts List */}
                <Route
                  path="/admin/posts"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <PostsList />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* New Post */}
                <Route
                  path="/admin/posts/new"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <PostEditor />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Edit Post */}
                <Route
                  path="/admin/posts/edit/:id"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <PostEditor />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Categories Manager */}
                <Route
                  path="/admin/categories"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <CategoriesManager />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Tags Manager */}
                <Route
                  path="/admin/tags"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <TagsManager />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Analytics */}
                <Route
                  path="/admin/analytics"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <AdminAnalytics />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Debug Dashboard */}
                <Route
                  path="/admin/debug"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <DebugPage />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Media Manager */}
                <Route
                  path="/admin/media"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <MediaManager />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Admin Settings */}
                <Route
                  path="/admin/settings"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <AdminSettings />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Users Manager */}
                <Route
                  path="/admin/users"
                  element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoader />}>
                        <DashboardLayout>
                          <UsersManager />
                        </DashboardLayout>
                      </Suspense>
                    </ProtectedRoute>
                  }
                />

                {/* Sobre */}
                <Route
                  path="/about"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <About />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Páginas Legais */}
                <Route
                  path="/legal/lgpd"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <LGPD />
                      </MainLayout>
                    </Suspense>
                  }
                />

                <Route
                  path="/legal/terms"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Terms />
                      </MainLayout>
                    </Suspense>
                  }
                />

                <Route
                  path="/legal/privacy"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Privacy />
                      </MainLayout>
                    </Suspense>
                  }
                />

                <Route
                  path="/legal/cookies"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Cookies />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Contato */}
                <Route
                  path="/contact"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Contact />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Páginas do Footer - Blog */}
                <Route
                  path="/archive"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Archive />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Páginas do Footer - Empresa */}
                <Route
                  path="/team"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Team />
                      </MainLayout>
                    </Suspense>
                  }
                />

                <Route
                  path="/careers"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Careers />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* Páginas do Footer - Recursos */}
                <Route
                  path="/docs"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Docs />
                      </MainLayout>
                    </Suspense>
                  }
                />

                <Route
                  path="/api"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <ApiDocs />
                      </MainLayout>
                    </Suspense>
                  }
                />

                <Route
                  path="/support"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Support />
                      </MainLayout>
                    </Suspense>
                  }
                />

                <Route
                  path="/status"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <MainLayout>
                        <Status />
                      </MainLayout>
                    </Suspense>
                  }
                />

                {/* 404 */}
                <Route
                  path="*"
                  element={
                    <div className="min-h-screen flex items-center justify-center">
                      <div className="text-center">
                        <h1 className="text-6xl font-bold text-neon-cyan mb-4">
                          404
                        </h1>
                        <p className="text-xl text-cyber-muted mb-6">
                          Página não encontrada
                        </p>
                        <a
                          href="/"
                          className="inline-block px-6 py-3 bg-gradient-neon text-cyber-bg rounded-lg font-medium hover:scale-105 transition-transform">
                          Voltar ao Início
                        </a>
                      </div>
                    </div>
                  }
                />
              </Routes>
            </AnalyticsWrapper>
          </ToastProvider>
        </AuthProvider>
        {/* Vercel Analytics */}
        <Analytics />
      </Router>
    </HelmetProvider>
  );
}

export default App;
