/**
 * @fileoverview Layout principal da aplicação com tema cyberpunk neon
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps } from '../../types';
import { BackToTop } from '../ui/BackToTop';
import { BreakingNews } from '../ui/BreakingNews';
import { CookieBanner } from '../legal/CookieBanner';
import { FooterNeon } from './FooterNeon';
import { HeaderComplex } from './HeaderComplex';

// ============================================================================
// INTERFACES
// ============================================================================

interface MainLayoutProps extends BaseComponentProps {
  /**
   * Se deve mostrar o header
   */
  showHeader?: boolean;

  /**
   * Se deve mostrar o footer
   */
  showFooter?: boolean;

  /**
   * Se deve mostrar o breaking news
   */
  showBreakingNews?: boolean;

  /**
   * Se deve mostrar a sidebar
   */
  showSidebar?: boolean;

  /**
   * Conteúdo da sidebar
   */
  sidebar?: React.ReactNode;

  /**
   * Se o layout deve ter container
   */
  containerized?: boolean;

  /**
   * Se deve ter padding no main
   */
  padded?: boolean;

  /**
   * Classe personalizada para o main
   */
  mainClassName?: string;

  /**
   * Classe personalizada para o container
   */
  containerClassName?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Layout principal da aplicação
 */
export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  className,
  showHeader = true,
  showFooter = true,
  showBreakingNews = true,
  showSidebar = false,
  sidebar,
  containerized = true,
  padded = true,
  mainClassName,
  containerClassName,
  ...props
}) => {
  const layoutClasses = twMerge(
    clsx('min-h-screen bg-cyber-bg text-cyber-text', 'flex flex-col', className)
  );

  const mainClasses = twMerge(clsx('flex-1', padded && 'py-8', mainClassName));

  const containerClasses = twMerge(
    clsx(
      containerized && ['container mx-auto px-4', 'max-w-7xl'],
      containerClassName
    )
  );

  const contentClasses = clsx(
    showSidebar ? 'grid grid-cols-1 lg:grid-cols-4 gap-8' : 'w-full'
  );

  return (
    <div className={layoutClasses} {...props}>
      {/* Header Complexo Unificado */}
      {showHeader && <HeaderComplex showBreakingNews={false} />}

      {/* Breaking News - Separado e melhor posicionado */}
      {showBreakingNews && <BreakingNews />}

      {/* Main Content */}
      <main className={mainClasses}>
        <div className={containerClasses}>
          <div className={contentClasses}>
            {/* Sidebar */}
            {showSidebar && sidebar && (
              <aside className="lg:col-span-1 order-2 lg:order-1">
                <div className="sticky top-8 space-y-6">{sidebar}</div>
              </aside>
            )}

            {/* Content */}
            <div
              className={clsx(
                showSidebar ? 'lg:col-span-3 order-1 lg:order-2' : 'w-full'
              )}>
              {children}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      {showFooter && <FooterNeon />}

      {/* Back to Top Button */}
      <BackToTop threshold={300} position="bottom-right" neonEffect={true} />

      {/* Cookie Banner */}
      <CookieBanner
        onAccept={(preferences) => {
          console.log('Cookies aceitos:', preferences);
          // Aqui você pode integrar com analytics, etc.
        }}
        onReject={() => {
          console.log('Cookies rejeitados');
        }}
        onSavePreferences={(preferences) => {
          console.log('Preferências salvas:', preferences);
        }}
      />
    </div>
  );
};

export default MainLayout;
