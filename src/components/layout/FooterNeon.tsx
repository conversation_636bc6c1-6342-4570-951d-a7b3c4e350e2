/**
 * @fileoverview Footer com tema cyberpunk neon
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import {
  FaGithub,
  FaGlobe,
  FaInstagram,
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'react-icons/fa';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps, SocialLinks } from '../../types';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

// ============================================================================
// INTERFACES
// ============================================================================

interface FooterNeonProps extends BaseComponentProps {
  /**
   * Se deve mostrar a newsletter
   */
  showNewsletter?: boolean;

  /**
   * Se deve mostrar os links sociais
   */
  showSocial?: boolean;

  /**
   * Links sociais personalizados
   */
  socialLinks?: SocialLinks;

  /**
   * Se deve mostrar as estatísticas
   */
  showStats?: boolean;

  /**
   * Função para inscrição na newsletter
   */
  onNewsletterSubmit?: (email: string) => void;
}

// ============================================================================
// DADOS MOCKADOS
// ============================================================================

const defaultSocialLinks: SocialLinks = {
  twitter: 'https://twitter.com/blueprintblog',
  linkedin: 'https://linkedin.com/company/blueprintblog',
  github: 'https://github.com/blueprintblog',
  website: 'https://blueprintblog.tech',
  instagram: 'https://instagram.com/blueprintblog',
};

const footerLinks = {
  blog: [
    { label: 'Últimos Posts', href: '/blog' },
    { label: 'Categorias', href: '/blog?filter=categories' },
    { label: 'Tags', href: '/blog?filter=tags' },
    { label: 'Arquivo', href: '/archive' },
  ],
  company: [
    { label: 'Sobre Nós', href: '/about' },
    { label: 'Equipe', href: '/team' },
    { label: 'Contato', href: '/contact' },
    { label: 'Carreiras', href: '/careers' },
  ],
  // 🚫 Recursos temporariamente removidos - aguardando implementação completa
  // resources: [
  //   { label: 'Documentação', href: '/docs' },
  //   { label: 'API', href: '/api' },
  //   { label: 'Suporte', href: '/support' },
  //   { label: 'Status', href: '/status' },
  // ],
  legal: [
    { label: 'Privacidade', href: '/legal/privacy' },
    { label: 'Termos', href: '/legal/terms' },
    { label: 'Cookies', href: '/legal/cookies' },
    { label: 'LGPD', href: '/legal/lgpd' },
  ],
};

// Stats temporariamente desabilitadas
// const stats = [
//   { label: 'Posts Publicados', value: '150+' },
//   { label: 'Leitores Mensais', value: '50K+' },
//   { label: 'Países Alcançados', value: '25+' },
//   { label: 'Tempo Online', value: '99.9%' },
// ];

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Footer principal com links e newsletter
 */
export const FooterNeon: React.FC<FooterNeonProps> = ({
  className,
  showNewsletter = true,
  showSocial = true,
  socialLinks = defaultSocialLinks,
  showStats = true,
  onNewsletterSubmit,
  ...props
}) => {
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsSubscribing(true);

    try {
      if (onNewsletterSubmit) {
        await onNewsletterSubmit(email.trim());
      }
      setEmail('');
      // TODO: Mostrar toast de sucesso
    } catch (error) {
      // TODO: Mostrar toast de erro
    } finally {
      setIsSubscribing(false);
    }
  };

  const footerClasses = twMerge(
    clsx('border-t border-cyber-border bg-cyber-surface', 'mt-auto', className)
  );

  return (
    <footer className={footerClasses} {...props}>
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand & Description */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-neon rounded-lg flex items-center justify-center">
                <span className="text-cyber-bg font-bold text-lg">B</span>
              </div>
              <div>
                <h3 className="text-lg font-bold">
                  <span className="bg-gradient-neon bg-clip-text text-transparent">
                    BLUEPRINT
                  </span>
                </h3>
                <p className="text-xs text-cyber-muted">BLOG v2.0</p>
              </div>
            </div>
            <p className="text-cyber-muted text-sm mb-6">
              Blueprint é mais que um blog. É um ponto de encontro para quem
              busca liberdade digital através da criação. Um portfólio aberto,
              um laboratório de ideias, um manifesto vivo. Aqui, todos podem
              sair da Matrix e criar seu próprio caminho. Se você constrói,
              escreve, experimenta ou compartilha — este espaço é para você.
              Blueprint não é um produto fechado. É um movimento em constante
              evolução.
            </p>

            {/* Social Links */}
            {showSocial && (
              <div className="flex space-x-3">
                {socialLinks.twitter && (
                  <a
                    href={socialLinks.twitter}
                    className="group relative p-2 rounded-lg text-cyber-muted hover:text-white transition-all duration-300 hover:bg-[#1DA1F2]/10 hover:shadow-lg hover:shadow-[#1DA1F2]/30"
                    aria-label="Twitter"
                    target="_blank"
                    rel="noopener noreferrer">
                    <FaTwitter className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                    <div className="absolute inset-0 rounded-lg bg-[#1DA1F2]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10"></div>
                  </a>
                )}
                {socialLinks.linkedin && (
                  <a
                    href={socialLinks.linkedin}
                    className="group relative p-2 rounded-lg text-cyber-muted hover:text-white transition-all duration-300 hover:bg-[#0077B5]/10 hover:shadow-lg hover:shadow-[#0077B5]/30"
                    aria-label="LinkedIn"
                    target="_blank"
                    rel="noopener noreferrer">
                    <FaLinkedin className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                    <div className="absolute inset-0 rounded-lg bg-[#0077B5]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10"></div>
                  </a>
                )}
                {socialLinks.github && (
                  <a
                    href={socialLinks.github}
                    className="group relative p-2 rounded-lg text-cyber-muted hover:text-white transition-all duration-300 hover:bg-white/10 hover:shadow-lg hover:shadow-white/30"
                    aria-label="GitHub"
                    target="_blank"
                    rel="noopener noreferrer">
                    <FaGithub className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                    <div className="absolute inset-0 rounded-lg bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10"></div>
                  </a>
                )}
                {socialLinks.instagram && (
                  <a
                    href={socialLinks.instagram}
                    className="group relative p-2 rounded-lg text-cyber-muted hover:text-white transition-all duration-300 hover:bg-gradient-to-r hover:from-[#E4405F]/10 hover:to-[#F77737]/10 hover:shadow-lg hover:shadow-[#E4405F]/30"
                    aria-label="Instagram"
                    target="_blank"
                    rel="noopener noreferrer">
                    <FaInstagram className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                    <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-[#E4405F]/20 to-[#F77737]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10"></div>
                  </a>
                )}
                {socialLinks.website && (
                  <a
                    href={socialLinks.website}
                    className="group relative p-2 rounded-lg text-cyber-muted hover:text-white transition-all duration-300 hover:bg-neon-cyan/10 hover:shadow-lg hover:shadow-neon-cyan/30"
                    aria-label="Website"
                    target="_blank"
                    rel="noopener noreferrer">
                    <FaGlobe className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                    <div className="absolute inset-0 rounded-lg bg-neon-cyan/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10"></div>
                  </a>
                )}
              </div>
            )}
          </div>

          {/* Links Columns */}
          <div className="grid grid-cols-2 gap-8 lg:col-span-3">
            {/* Blog Links */}
            <div>
              <h4 className="text-cyber-text font-semibold mb-4">Blog</h4>
              <ul className="space-y-2">
                {footerLinks.blog.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className="text-cyber-muted hover:text-neon-cyan transition-colors duration-200 text-sm">
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company Links */}
            <div>
              <h4 className="text-cyber-text font-semibold mb-4">Empresa</h4>
              <ul className="space-y-2">
                {footerLinks.company.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className="text-cyber-muted hover:text-neon-cyan transition-colors duration-200 text-sm">
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* 🚫 Resources Links - Temporariamente removido */}
            {/* <div>
              <h4 className="text-cyber-text font-semibold mb-4">Recursos</h4>
              <ul className="space-y-2">
                {footerLinks.resources?.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className="text-cyber-muted hover:text-neon-cyan transition-colors duration-200 text-sm">
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div> */}
          </div>
        </div>
        {/* Newsletter Section */}
        {showNewsletter && (
          <div className="mt-12 pt-8 border-t border-cyber-border">
            <div className="max-w-md mx-auto text-center">
              <h4 className="text-cyber-text font-semibold mb-2">
                📧 Newsletter Cyberpunk
              </h4>
              <p className="text-cyber-muted text-sm mb-4">
                Receba as últimas novidades sobre tecnologia e desenvolvimento
              </p>
              <form onSubmit={handleNewsletterSubmit} className="flex gap-2">
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="flex-1"
                  size="sm"
                />
                <Button
                  type="submit"
                  loading={isSubscribing}
                  disabled={!email.trim()}
                  size="sm">
                  Inscrever
                </Button>
              </form>
            </div>
          </div>
        )}
        {/* Stats Section */}
        {/*🚫 Temporariamente Desanilitado */}
        {/* {showStats && (
          <div className="mt-8 pt-8 border-t border-cyber-border">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              {stats.map((stat, index) => (
                <div key={index} className="space-y-1">
                  <div className="text-2xl font-bold text-neon-cyan">
                    {stat.value}
                  </div>
                  <div className="text-xs text-cyber-muted">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        )} */}{' '}
        /
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-cyber-border bg-cyber-bg">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
            <div className="text-cyber-muted text-sm">
              © 2025 Blueprint Blog v2.0. Todos os direitos reservados.
            </div>
            <div className="flex space-x-4">
              {footerLinks.legal.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="text-cyber-muted hover:text-neon-cyan transition-colors duration-200 text-xs">
                  {link.label}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default FooterNeon;
