/**
 * @fileoverview Header Mobile Simplificado e Otimizado
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { Menu, X, Search, Home, BookOpen, Tag, User } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';
import { LogoNeon } from '../ui/LogoNeon';

// ============================================================================
// INTERFACES
// ============================================================================

interface HeaderMobileProps {
  /** Classes CSS adicionais */
  className?: string;
  
  /** Se deve mostrar busca */
  showSearch?: boolean;
  
  /** Callback de busca */
  onSearch?: (query: string) => void;
  
  /** Se deve auto-hide no scroll */
  autoHide?: boolean;
}

interface MenuItem {
  id: string;
  label: string;
  href: string;
  icon: React.ReactNode;
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

const menuItems: MenuItem[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/',
    icon: <Home className="w-5 h-5" />,
  },
  {
    id: 'blog',
    label: 'Blog',
    href: '/blog',
    icon: <BookOpen className="w-5 h-5" />,
  },
  {
    id: 'categorias',
    label: 'Categorias',
    href: '/categorias',
    icon: <Tag className="w-5 h-5" />,
  },
  {
    id: 'sobre',
    label: 'Sobre',
    href: '/sobre',
    icon: <User className="w-5 h-5" />,
  },
];

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Header mobile simplificado e otimizado
 */
export const HeaderMobile: React.FC<HeaderMobileProps> = ({
  className,
  showSearch = true,
  onSearch,
  autoHide = true,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Auto-hide no scroll
  useEffect(() => {
    if (!autoHide) return;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down & past threshold
        setIsVisible(false);
        setIsMenuOpen(false);
        setIsSearchOpen(false);
      } else {
        // Scrolling up
        setIsVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY, autoHide]);

  // Handlers
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    setIsSearchOpen(false);
  };

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
    setIsMenuOpen(false);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearch?.(searchQuery.trim());
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleMenuItemClick = () => {
    setIsMenuOpen(false);
  };

  // Classes
  const headerClasses = twMerge(
    clsx(
      'fixed top-0 left-0 right-0 z-50',
      'bg-cyber-surface/95 backdrop-blur-md',
      'border-b border-cyber-border',
      'transition-transform duration-300 ease-in-out',
      isVisible ? 'translate-y-0' : '-translate-y-full',
      className
    )
  );

  return (
    <>
      <header className={headerClasses}>
        {/* Main Header Bar */}
        <div className="flex items-center justify-between h-14 px-4">
          {/* Logo */}
          <LogoNeon
            size="sm"
            hoverable={true}
            glitchEffect={false}
            neonIntensity="low"
            onClick={() => (window.location.href = '/')}
            className="scale-90"
          />

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {/* Search Button */}
            {showSearch && (
              <button
                onClick={toggleSearch}
                className={clsx(
                  'p-2 rounded-lg transition-colors',
                  'text-cyber-text hover:text-neon-cyan',
                  'hover:bg-cyber-border/30',
                  isSearchOpen && 'text-neon-cyan bg-cyber-border/30'
                )}
                aria-label="Buscar">
                <Search className="w-5 h-5" />
              </button>
            )}

            {/* Menu Button */}
            <button
              onClick={toggleMenu}
              className={clsx(
                'p-2 rounded-lg transition-colors',
                'text-cyber-text hover:text-neon-cyan',
                'hover:bg-cyber-border/30',
                isMenuOpen && 'text-neon-cyan bg-cyber-border/30'
              )}
              aria-label="Menu">
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <AnimatePresence>
          {isSearchOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="border-t border-cyber-border bg-cyber-surface/98">
              <form onSubmit={handleSearch} className="p-4">
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Buscar posts..."
                    className="w-full px-4 py-3 pl-10 bg-cyber-bg border border-cyber-border rounded-lg text-cyber-text placeholder-cyber-muted focus:outline-none focus:border-neon-cyan focus:ring-1 focus:ring-neon-cyan text-base"
                    autoFocus
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cyber-muted" />
                  {searchQuery && (
                    <button
                      type="button"
                      onClick={() => setSearchQuery('')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-cyber-muted hover:text-cyber-text">
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </form>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="border-t border-cyber-border bg-cyber-surface/98">
              <nav className="py-2">
                {menuItems.map((item, index) => (
                  <motion.a
                    key={item.id}
                    href={item.href}
                    onClick={handleMenuItemClick}
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center space-x-3 px-4 py-3 text-cyber-text hover:text-neon-cyan hover:bg-cyber-border/20 transition-colors">
                    {item.icon}
                    <span className="font-medium text-sm-mobile">{item.label}</span>
                  </motion.a>
                ))}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </header>

      {/* Spacer para compensar header fixo */}
      <div className="h-14" />

      {/* Overlay para fechar menu */}
      <AnimatePresence>
        {(isMenuOpen || isSearchOpen) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => {
              setIsMenuOpen(false);
              setIsSearchOpen(false);
            }}
            className="fixed inset-0 bg-cyber-bg/50 backdrop-blur-sm z-40"
          />
        )}
      </AnimatePresence>
    </>
  );
};

export default HeaderMobile;
