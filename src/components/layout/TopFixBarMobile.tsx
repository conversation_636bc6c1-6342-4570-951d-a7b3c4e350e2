/**
 * @fileoverview TopFixBar Mobile Otimizado
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { motion, AnimatePresence } from 'motion/react';
import { X, ExternalLink } from 'lucide-react';

// ============================================================================
// INTERFACES
// ============================================================================

interface TopFixBarMobileProps {
  /** Texto da notificação */
  text?: string;
  
  /** URL do link */
  href?: string;
  
  /** Texto do link */
  linkText?: string;
  
  /** Se pode ser fechado */
  dismissible?: boolean;
  
  /** Callback quando fechado */
  onDismiss?: () => void;
  
  /** Auto-hide no scroll */
  autoHide?: boolean;
  
  /** Classes CSS adicionais */
  className?: string;
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * TopFixBar mobile otimizado com auto-hide
 */
export const TopFixBarMobile: React.FC<TopFixBarMobileProps> = ({
  text = '🚀 Novo post: Como otimizar React para mobile',
  href = '/blog/react-mobile-optimization',
  linkText = 'Leia agora',
  dismissible = true,
  onDismiss,
  autoHide = true,
  className,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isDismissed, setIsDismissed] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Auto-hide no scroll
  useEffect(() => {
    if (!autoHide || isDismissed) return;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 50) {
        // Scrolling down & past threshold
        setIsVisible(false);
      } else if (currentScrollY < lastScrollY) {
        // Scrolling up
        setIsVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY, autoHide, isDismissed]);

  // Handler para dismiss
  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  // Se foi dismissed, não renderiza
  if (isDismissed) return null;

  // Classes
  const barClasses = twMerge(
    clsx(
      'fixed top-0 left-0 right-0 z-50',
      'bg-gradient-to-r from-neon-cyan/10 to-neon-magenta/10',
      'border-b border-neon-cyan/30',
      'backdrop-blur-sm',
      'transition-transform duration-300 ease-in-out',
      isVisible ? 'translate-y-0' : '-translate-y-full',
      className
    )
  );

  return (
    <AnimatePresence>
      {!isDismissed && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className={barClasses}>
          <div className="flex items-center justify-between px-3 py-2 min-h-[32px]">
            {/* Conteúdo principal */}
            <div className="flex-1 min-w-0 mr-2">
              <div className="flex items-center space-x-2">
                {/* Texto principal */}
                <span className="text-xs-mobile text-cyber-text font-medium truncate-mobile-1 flex-1">
                  {text}
                </span>
                
                {/* Link */}
                {href && linkText && (
                  <a
                    href={href}
                    className="flex items-center space-x-1 text-xs-mobile text-neon-cyan hover:text-neon-cyan/80 font-semibold whitespace-nowrap transition-colors">
                    <span>{linkText}</span>
                    <ExternalLink className="w-3 h-3" />
                  </a>
                )}
              </div>
            </div>

            {/* Botão de fechar */}
            {dismissible && (
              <button
                onClick={handleDismiss}
                className="flex-shrink-0 p-1 text-cyber-muted hover:text-cyber-text transition-colors"
                aria-label="Fechar notificação">
                <X className="w-3 h-3" />
              </button>
            )}
          </div>

          {/* Indicador de progresso (opcional) */}
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-neon-cyan to-neon-magenta opacity-50" />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// ============================================================================
// VARIAÇÕES ESPECIAIS
// ============================================================================

/**
 * TopFixBar para anúncios
 */
export const TopFixBarAnnouncement: React.FC<Omit<TopFixBarMobileProps, 'text' | 'href' | 'linkText'>> = ({
  ...props
}) => {
  return (
    <TopFixBarMobile
      text="📢 Novo curso: React + TypeScript do zero ao avançado"
      href="/cursos/react-typescript"
      linkText="Inscreva-se"
      {...props}
    />
  );
};

/**
 * TopFixBar para novos posts
 */
export const TopFixBarNewPost: React.FC<Omit<TopFixBarMobileProps, 'text'> & { postTitle?: string }> = ({
  postTitle = 'Como criar componentes React reutilizáveis',
  ...props
}) => {
  return (
    <TopFixBarMobile
      text={`✨ Novo post: ${postTitle}`}
      linkText="Leia agora"
      {...props}
    />
  );
};

/**
 * TopFixBar para atualizações
 */
export const TopFixBarUpdate: React.FC<Omit<TopFixBarMobileProps, 'text' | 'href' | 'linkText'>> = ({
  ...props
}) => {
  return (
    <TopFixBarMobile
      text="🔄 Site atualizado! Nova versão v2.0 com melhorias de performance"
      href="/changelog"
      linkText="Ver mudanças"
      {...props}
    />
  );
};

export default TopFixBarMobile;
