/**
 * @fileoverview Dashboard para Vercel Speed Insights - Métricas de Performance
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { clsx } from 'clsx';
import { 
  Activity, 
  Zap, 
  Clock, 
  Eye, 
  Gauge, 
  TrendingUp,
  ExternalLink,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { Button } from '../ui/Button';
import { useSpeedInsights } from '../../hooks/useSpeedInsights';

// ============================================================================
// INTERFACES
// ============================================================================

interface MetricCardProps {
  title: string;
  value: string;
  rating: 'good' | 'needs-improvement' | 'poor' | 'unknown';
  description: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'stable';
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

const ratingConfig = {
  good: {
    color: 'text-green-400',
    bgColor: 'bg-green-400/10',
    borderColor: 'border-green-400/30',
    label: 'Bom',
  },
  'needs-improvement': {
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-400/10',
    borderColor: 'border-yellow-400/30',
    label: 'Precisa Melhorar',
  },
  poor: {
    color: 'text-red-400',
    bgColor: 'bg-red-400/10',
    borderColor: 'border-red-400/30',
    label: 'Ruim',
  },
  unknown: {
    color: 'text-cyber-muted',
    bgColor: 'bg-cyber-surface',
    borderColor: 'border-cyber-border',
    label: 'Carregando...',
  },
};

// ============================================================================
// COMPONENTES
// ============================================================================

/**
 * Card individual de métrica
 */
const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  rating,
  description,
  icon,
  trend,
}) => {
  const config = ratingConfig[rating];

  return (
    <Card className={clsx('relative overflow-hidden', config.borderColor)}>
      <div className={clsx('absolute inset-0 opacity-5', config.bgColor)} />
      
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={clsx('p-1 rounded', config.bgColor, config.color)}>
              {icon}
            </div>
            <h3 className="font-semibold text-cyber-text text-sm">{title}</h3>
          </div>
          
          {trend && (
            <div className={clsx(
              'text-xs px-2 py-1 rounded',
              trend === 'up' ? 'text-green-400 bg-green-400/10' :
              trend === 'down' ? 'text-red-400 bg-red-400/10' :
              'text-cyber-muted bg-cyber-surface'
            )}>
              {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-2">
          <div className={clsx('text-2xl font-bold', config.color)}>
            {value}
          </div>
          
          <div className="flex items-center justify-between">
            <span className={clsx('text-xs px-2 py-1 rounded', config.bgColor, config.color)}>
              {config.label}
            </span>
          </div>
          
          <p className="text-xs text-cyber-muted leading-relaxed">
            {description}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Dashboard principal do Speed Insights
 */
export const SpeedInsightsDashboard: React.FC = () => {
  const { getCurrentMetrics, formatMetricValue, getMetricRating } = useSpeedInsights();
  const [metrics, setMetrics] = useState<any>({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  /**
   * Carregar métricas
   */
  const loadMetrics = () => {
    setIsLoading(true);
    
    // Simular delay para mostrar loading
    setTimeout(() => {
      const currentMetrics = getCurrentMetrics();
      setMetrics(currentMetrics);
      setLastUpdate(new Date());
      setIsLoading(false);
    }, 1000);
  };

  /**
   * Carregar métricas na inicialização
   */
  useEffect(() => {
    loadMetrics();
  }, []);

  /**
   * Dados das métricas para exibição
   */
  const metricsData = [
    {
      title: 'FCP',
      value: metrics.fcp ? formatMetricValue('fcp', metrics.fcp) : 'N/A',
      rating: metrics.fcp ? getMetricRating('fcp', metrics.fcp) : 'unknown' as const,
      description: 'First Contentful Paint - Tempo até o primeiro conteúdo aparecer na tela',
      icon: <Eye className="w-4 h-4" />,
    },
    {
      title: 'LCP',
      value: metrics.lcp ? formatMetricValue('lcp', metrics.lcp) : 'N/A',
      rating: metrics.lcp ? getMetricRating('lcp', metrics.lcp) : 'unknown' as const,
      description: 'Largest Contentful Paint - Tempo até o maior elemento aparecer',
      icon: <Activity className="w-4 h-4" />,
    },
    {
      title: 'TTFB',
      value: metrics.ttfb ? formatMetricValue('ttfb', metrics.ttfb) : 'N/A',
      rating: metrics.ttfb ? getMetricRating('ttfb', metrics.ttfb) : 'unknown' as const,
      description: 'Time to First Byte - Tempo de resposta do servidor',
      icon: <Zap className="w-4 h-4" />,
    },
    {
      title: 'CLS',
      value: metrics.cls ? formatMetricValue('cls', metrics.cls) : 'N/A',
      rating: metrics.cls ? getMetricRating('cls', metrics.cls) : 'unknown' as const,
      description: 'Cumulative Layout Shift - Estabilidade visual da página',
      icon: <Gauge className="w-4 h-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-cyber-text mb-2">
            ⚡ Speed Insights
          </h2>
          <p className="text-cyber-muted">
            Métricas de performance em tempo real do Vercel
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            variant="secondary"
            size="sm"
            onClick={loadMetrics}
            disabled={isLoading}
            leftIcon={<RefreshCw className={clsx('w-4 h-4', isLoading && 'animate-spin')} />}>
            Atualizar
          </Button>

          <a
            href="https://vercel.com/analytics/speed"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center space-x-2 px-3 py-2 bg-cyber-surface hover:bg-cyber-border text-cyber-text rounded-lg transition-colors text-sm">
            <TrendingUp className="w-4 h-4" />
            <span>Vercel Dashboard</span>
            <ExternalLink className="w-3 h-3" />
          </a>
        </div>
      </div>

      {/* Última atualização */}
      <div className="flex items-center space-x-2 text-xs text-cyber-muted">
        <Clock className="w-3 h-3" />
        <span>
          Última atualização: {lastUpdate.toLocaleTimeString('pt-BR')}
        </span>
      </div>

      {/* Grid de métricas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metricsData.map((metric, index) => (
          <MetricCard
            key={metric.title}
            {...metric}
            trend={index % 3 === 0 ? 'up' : index % 3 === 1 ? 'stable' : 'down'}
          />
        ))}
      </div>

      {/* Informações adicionais */}
      <Card>
        <CardHeader>
          <h3 className="font-semibold text-cyber-text">
            📊 Sobre as Métricas
          </h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-cyber-muted">
            <div>
              <h4 className="font-medium text-cyber-text mb-2">Core Web Vitals</h4>
              <ul className="space-y-1">
                <li>• <strong>LCP:</strong> Deve ser ≤ 2.5s</li>
                <li>• <strong>FID:</strong> Deve ser ≤ 100ms</li>
                <li>• <strong>CLS:</strong> Deve ser ≤ 0.1</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-cyber-text mb-2">Outras Métricas</h4>
              <ul className="space-y-1">
                <li>• <strong>FCP:</strong> Deve ser ≤ 1.8s</li>
                <li>• <strong>TTFB:</strong> Deve ser ≤ 800ms</li>
                <li>• <strong>INP:</strong> Deve ser ≤ 200ms</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status do Vercel Speed Insights */}
      <Card className="border-neon-cyan/30">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-sm text-cyber-text">
              Vercel Speed Insights ativo - Coletando métricas em tempo real
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SpeedInsightsDashboard;
