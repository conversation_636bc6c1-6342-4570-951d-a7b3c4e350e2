import { Helmet } from 'react-helmet-async';
import type { SEOData } from '../../types';

interface SEOHeadProps {
  seo: SEOData;
  children?: React.ReactNode;
}

const SEOHead: React.FC<SEOHeadProps> = ({ seo, children }) => {
  const {
    title,
    description,
    keywords,
    canonical,
    ogTitle,
    ogDescription,
    ogImage,
    ogType = 'website',
    twitterCard = 'summary_large_image',
    twitterTitle,
    twitterDescription,
    twitterImage,
    author,
    publishedTime,
    modifiedTime,
    section,
    tags,
    locale = 'pt_BR',
    siteName = 'Blueprint Blog',
  } = seo;

  // Fallbacks para Open Graph
  const finalOgTitle = ogTitle || title;
  const finalOgDescription = ogDescription || description;
  const finalOgImage = ogImage || '/images/og-default.jpg';

  // Fallbacks para Twitter
  const finalTwitterTitle = twitterTitle || finalOgTitle;
  const finalTwitterDescription = twitterDescription || finalOgDescription;
  const finalTwitterImage = twitterImage || finalOgImage;

  // URL base do site
  const siteUrl = 'https://blueprintblog.tech';
  const fullCanonical = canonical?.startsWith('http')
    ? canonical
    : `${siteUrl}${canonical || ''}`;

  return (
    <Helmet>
      {/* Meta Tags Básicas */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={fullCanonical} />

      {/* Meta Tags de Autor e Data */}
      {author && <meta name="author" content={author} />}
      {publishedTime && (
        <meta name="article:published_time" content={publishedTime} />
      )}
      {modifiedTime && (
        <meta name="article:modified_time" content={modifiedTime} />
      )}

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={finalOgTitle} />
      <meta property="og:description" content={finalOgDescription} />
      <meta property="og:image" content={finalOgImage} />
      <meta property="og:url" content={fullCanonical} />
      <meta property="og:type" content={ogType} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />

      {/* Open Graph para Articles */}
      {ogType === 'article' && (
        <>
          {author && <meta property="article:author" content={author} />}
          {publishedTime && (
            <meta property="article:published_time" content={publishedTime} />
          )}
          {modifiedTime && (
            <meta property="article:modified_time" content={modifiedTime} />
          )}
          {section && <meta property="article:section" content={section} />}
          {tags &&
            tags.map((tag, index) => (
              <meta key={index} property="article:tag" content={tag} />
            ))}
        </>
      )}

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={finalTwitterTitle} />
      <meta name="twitter:description" content={finalTwitterDescription} />
      <meta name="twitter:image" content={finalTwitterImage} />
      <meta name="twitter:site" content="@blueprintblog" />
      <meta name="twitter:creator" content="@blueprintblog" />

      {/* Meta Tags Adicionais para SEO */}
      <meta name="theme-color" content="#00ffff" />
      <meta name="msapplication-TileColor" content="#00ffff" />
      <meta name="application-name" content={siteName} />

      {/* Preconnect para Performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link
        rel="preconnect"
        href="https://fonts.gstatic.com"
        crossOrigin="anonymous"
      />
      <link rel="preconnect" href="https://www.google-analytics.com" />

      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//vercel.live" />

      {/* Viewport e Mobile */}
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=5.0"
      />
      <meta name="format-detection" content="telephone=no" />

      {/* Segurança */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta
        httpEquiv="Content-Security-Policy"
        content="object-src 'none'; base-uri 'self';"
      />

      {/* Conteúdo adicional personalizado */}
      {children}
    </Helmet>
  );
};

export default SEOHead;
