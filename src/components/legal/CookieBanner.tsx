/**
 * @fileoverview Cookie Banner Component v2 - Blueprint Blog
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { FiX, FiSettings, FiInfo, FiCheck, FiShield } from 'react-icons/fi';
import { Button } from '../ui/Button';

// ============================================================================
// INTERFACES
// ============================================================================

interface CookiePreferences {
  essential: boolean;
  analytics: boolean;
  marketing: boolean;
  functional: boolean;
}

interface CookieBannerProps {
  /**
   * Função chamada quando o usuário aceita os cookies
   */
  onAccept?: (preferences: CookiePreferences) => void;

  /**
   * Função chamada quando o usuário rejeita cookies não essenciais
   */
  onReject?: () => void;

  /**
   * Função chamada quando o usuário salva preferências customizadas
   */
  onSavePreferences?: (preferences: CookiePreferences) => void;

  /**
   * Classe CSS adicional
   */
  className?: string;
}

// ============================================================================
// CONSTANTS
// ============================================================================

const COOKIE_CONSENT_KEY = 'blueprint_cookie_consent';
const COOKIE_PREFERENCES_KEY = 'blueprint_cookie_preferences';

const DEFAULT_PREFERENCES: CookiePreferences = {
  essential: true, // Sempre true, não pode ser desabilitado
  analytics: false,
  marketing: false,
  functional: false,
};

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Banner de consentimento de cookies seguindo padrões LGPD
 */
export const CookieBanner: React.FC<CookieBannerProps> = ({
  onAccept,
  onReject,
  onSavePreferences,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>(DEFAULT_PREFERENCES);

  // Verificar se já existe consentimento
  useEffect(() => {
    const hasConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (!hasConsent) {
      setIsVisible(true);
    }

    // Carregar preferências salvas
    const savedPreferences = localStorage.getItem(COOKIE_PREFERENCES_KEY);
    if (savedPreferences) {
      try {
        setPreferences(JSON.parse(savedPreferences));
      } catch (error) {
        console.warn('Erro ao carregar preferências de cookies:', error);
      }
    }
  }, []);

  // Aceitar todos os cookies
  const handleAcceptAll = () => {
    const allAccepted: CookiePreferences = {
      essential: true,
      analytics: true,
      marketing: true,
      functional: true,
    };

    localStorage.setItem(COOKIE_CONSENT_KEY, 'accepted');
    localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(allAccepted));
    
    setIsVisible(false);
    onAccept?.(allAccepted);
  };

  // Rejeitar cookies não essenciais
  const handleRejectAll = () => {
    const onlyEssential: CookiePreferences = {
      essential: true,
      analytics: false,
      marketing: false,
      functional: false,
    };

    localStorage.setItem(COOKIE_CONSENT_KEY, 'rejected');
    localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(onlyEssential));
    
    setIsVisible(false);
    onReject?.();
  };

  // Salvar preferências customizadas
  const handleSavePreferences = () => {
    localStorage.setItem(COOKIE_CONSENT_KEY, 'custom');
    localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(preferences));
    
    setIsVisible(false);
    onSavePreferences?.(preferences);
  };

  // Atualizar preferência específica
  const updatePreference = (key: keyof CookiePreferences, value: boolean) => {
    if (key === 'essential') return; // Essenciais não podem ser desabilitados
    
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
        className={`fixed bottom-0 left-0 right-0 z-50 ${className}`}
      >
        <div className="bg-cyber-surface/95 backdrop-blur-md border-t border-cyber-surface shadow-2xl">
          <div className="max-w-7xl mx-auto p-6">
            {!showDetails ? (
              // Banner Principal
              <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
                <div className="flex-1">
                  <div className="flex items-start gap-3 mb-4">
                    <FiShield className="w-6 h-6 text-neon-cyan mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="text-white font-semibold text-lg mb-2">
                        Cookies e Privacidade
                      </h3>
                      <p className="text-cyber-text text-sm leading-relaxed">
                        Usamos cookies para melhorar sua experiência, personalizar conteúdo
                        e analisar nosso tráfego. Você pode escolher quais cookies aceitar.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-cyber-muted">
                    <FiInfo className="w-4 h-4" />
                    <span>
                      Ao continuar navegando, você concorda com nossa{' '}
                      <a 
                        href="/legal/cookies" 
                        className="text-neon-cyan hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Política de Cookies
                      </a>
                    </span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDetails(true)}
                    className="border-cyber-surface text-cyber-text hover:text-white"
                  >
                    <FiSettings className="w-4 h-4 mr-2" />
                    Personalizar
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRejectAll}
                    className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                  >
                    Rejeitar
                  </Button>
                  
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleAcceptAll}
                    className="bg-neon-cyan text-cyber-bg hover:bg-neon-cyan/80"
                  >
                    <FiCheck className="w-4 h-4 mr-2" />
                    Aceitar Todos
                  </Button>
                </div>
              </div>
            ) : (
              // Painel de Configurações Detalhadas
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-white font-semibold text-lg">
                    Configurações de Cookies
                  </h3>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="text-cyber-muted hover:text-white transition-colors"
                  >
                    <FiX className="w-5 h-5" />
                  </button>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  {/* Cookies Essenciais */}
                  <div className="bg-cyber-bg/50 p-4 rounded-lg border border-green-500/30">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium">Cookies Essenciais</h4>
                      <div className="bg-green-500 text-white text-xs px-2 py-1 rounded">
                        Sempre Ativo
                      </div>
                    </div>
                    <p className="text-cyber-text text-sm">
                      Necessários para o funcionamento básico do site.
                      Não podem ser desabilitados.
                    </p>
                  </div>

                  {/* Cookies de Analytics */}
                  <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium">Analytics</h4>
                      <button
                        onClick={() => updatePreference('analytics', !preferences.analytics)}
                        className={`w-12 h-6 rounded-full transition-colors ${
                          preferences.analytics ? 'bg-neon-cyan' : 'bg-cyber-surface'
                        }`}
                      >
                        <div
                          className={`w-5 h-5 bg-white rounded-full transition-transform ${
                            preferences.analytics ? 'translate-x-6' : 'translate-x-0.5'
                          }`}
                        />
                      </button>
                    </div>
                    <p className="text-cyber-text text-sm">
                      Nos ajudam a entender como você usa o site para melhorar a experiência.
                    </p>
                  </div>

                  {/* Cookies Funcionais */}
                  <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium">Funcionais</h4>
                      <button
                        onClick={() => updatePreference('functional', !preferences.functional)}
                        className={`w-12 h-6 rounded-full transition-colors ${
                          preferences.functional ? 'bg-neon-cyan' : 'bg-cyber-surface'
                        }`}
                      >
                        <div
                          className={`w-5 h-5 bg-white rounded-full transition-transform ${
                            preferences.functional ? 'translate-x-6' : 'translate-x-0.5'
                          }`}
                        />
                      </button>
                    </div>
                    <p className="text-cyber-text text-sm">
                      Lembram suas preferências para personalizar sua experiência.
                    </p>
                  </div>

                  {/* Cookies de Marketing */}
                  <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium">Marketing</h4>
                      <button
                        onClick={() => updatePreference('marketing', !preferences.marketing)}
                        className={`w-12 h-6 rounded-full transition-colors ${
                          preferences.marketing ? 'bg-neon-cyan' : 'bg-cyber-surface'
                        }`}
                      >
                        <div
                          className={`w-5 h-5 bg-white rounded-full transition-transform ${
                            preferences.marketing ? 'translate-x-6' : 'translate-x-0.5'
                          }`}
                        />
                      </button>
                    </div>
                    <p className="text-cyber-text text-sm">
                      Usados para rastrear visitantes e exibir anúncios relevantes.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRejectAll}
                    className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                  >
                    Rejeitar Todos
                  </Button>
                  
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleSavePreferences}
                    className="bg-neon-cyan text-cyber-bg hover:bg-neon-cyan/80"
                  >
                    <FiCheck className="w-4 h-4 mr-2" />
                    Salvar Preferências
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default CookieBanner;
