import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiX, FiSettings, FiInfo } from 'react-icons/fi';
import { Button } from '../ui/Button';

// ============================================================================
// INTERFACES
// ============================================================================

interface CookiePreferences {
    essential: boolean;
    analytics: boolean;
    marketing: boolean;
    functional: boolean;
}

interface CookieBannerProps {
    /**
     * Função chamada quando o usuário aceita os cookies
     */
    onAccept?: (preferences: CookiePreferences) => void;

    /**
     * Função chamada quando o usuário rejeita cookies não essenciais
     */
    onReject?: () => void;

    /**
     * Função chamada quando o usuário salva preferências customizadas
     */
    onSavePreferences?: (preferences: CookiePreferences) => void;
}

// ============================================================================
// CONSTANTS
// ============================================================================

const COOKIE_CONSENT_KEY = 'blueprint_cookie_consent';
const COOKIE_PREFERENCES_KEY = 'blueprint_cookie_preferences';

// ============================================================================
// COMPONENT
// ============================================================================

export const CookieBanner: React.FC<CookieBannerProps> = ({
                                                              onAccept,
                                                              onReject,
                                                              onSavePreferences
                                                          }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [showDetails, setShowDetails] = useState(false);
    const [preferences, setPreferences] = useState<CookiePreferences>({
        essential: true, // Sempre true, não pode ser desabilitado
        analytics: true,
        marketing: false,
        functional: true
    });

    // Verificar se o usuário já deu consentimento
    useEffect(() => {
        const consent = localStorage.getItem(COOKIE_CONSENT_KEY);
        if (!consent) {
            // Delay para não interferir no carregamento inicial
            const timer = setTimeout(() => {
                setIsVisible(true);
            }, 2000);
            return () => clearTimeout(timer);
        } else {
            // Carregar preferências salvas
            const savedPreferences = localStorage.getItem(COOKIE_PREFERENCES_KEY);
            if (savedPreferences) {
                setPreferences(JSON.parse(savedPreferences));
            }
        }
    }, []);

    const handleAcceptAll = () => {
        const allPreferences: CookiePreferences = {
            essential: true,
            analytics: true,
            marketing: true,
            functional: true
        };

        localStorage.setItem(COOKIE_CONSENT_KEY, 'accepted');
        localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(allPreferences));

        setIsVisible(false);
        onAccept?.(allPreferences);
    };

    const handleRejectAll = () => {
        const essentialOnly: CookiePreferences = {
            essential: true,
            analytics: false,
            marketing: false,
            functional: false
        };

        localStorage.setItem(COOKIE_CONSENT_KEY, 'rejected');
        localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(essentialOnly));

        setIsVisible(false);
        onReject?.();
    };

    const handleSavePreferences = () => {
        localStorage.setItem(COOKIE_CONSENT_KEY, 'customized');
        localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(preferences));

        setIsVisible(false);
        onSavePreferences?.(preferences);
    };

    const handlePreferenceChange = (type: keyof CookiePreferences, value: boolean) => {
        if (type === 'essential') return; // Não permite desabilitar cookies essenciais

        setPreferences(prev => ({
            ...prev,
            [type]: value
        }));
    };

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.div
                    initial={{ y: 100, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 100, opacity: 0 }}
                    transition={{ type: "spring", damping: 25, stiffness: 200 }}
                    className="fixed bottom-0 left-0 right-0 z-50 bg-cyber-surface/95 backdrop-blur-lg border-t border-neon-cyan/30 shadow-2xl"
                >
                    <div className="max-w-7xl mx-auto p-6">
                        {!showDetails ? (
                            // Banner Simples
                            <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                                <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-3">
                                        <FiInfo className="text-neon-cyan text-xl flex-shrink-0" />
                                        <h3 className="text-white font-semibold">
                                            Usamos cookies para melhorar sua experiência
                                        </h3>
                                    </div>
                                    <p className="text-cyber-muted text-sm leading-relaxed">
                                        Este site utiliza cookies essenciais para funcionar e cookies analíticos para
                                        entender como você interage com nosso conteúdo. Ao continuar navegando, você
                                        concorda com nossa{' '}
                                        <a
                                            href="/cookies"
                                            className="text-neon-cyan hover:underline"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            Política de Cookies
                                        </a>
                                        {' '}e{' '}
                                        <a
                                            href="/privacy"
                                            className="text-neon-cyan hover:underline"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            Política de Privacidade
                                        </a>
                                        .
                                    </p>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-3 min-w-fit">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => setShowDetails(true)}
                                        className="border-cyber-surface text-cyber-muted hover:text-white hover:border-neon-cyan/50"
                                    >
                                        <FiSettings className="w-4 h-4 mr-2" />
                                        Personalizar
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={handleRejectAll}
                                        className="border-red-500/50 text-red-400 hover:bg-red-500/10 hover:border-red-500"
                                    >
                                        Rejeitar
                                    </Button>
                                    <Button
                                        variant="primary"
                                        size="sm"
                                        onClick={handleAcceptAll}
                                        className="bg-neon-cyan text-cyber-bg hover:bg-neon-cyan/90"
                                    >
                                        Aceitar Todos
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            // Configurações Detalhadas
                            <div className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <FiSettings className="text-neon-cyan text-xl" />
                                        <h3 className="text-white font-semibold text-lg">
                                            Configurações de Cookies
                                        </h3>
                                    </div>
                                    <button
                                        onClick={() => setShowDetails(false)}
                                        className="text-cyber-muted hover:text-white transition-colors p-2"
                                    >
                                        <FiX className="w-5 h-5" />
                                    </button>
                                </div>

                                <div className="grid md:grid-cols-2 gap-4">
                                    {/* Cookies Essenciais */}
                                    <div className="bg-cyber-bg/50 p-4 rounded-lg border border-green-500/30">
                                        <div className="flex items-center justify-between mb-3">
                                            <h4 className="text-white font-semibold">Cookies Essenciais</h4>
                                            <div className="bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-medium">
                                                Sempre Ativo
                                            </div>
                                        </div>
                                        <p className="text-cyber-muted text-sm">
                                            Necessários para o funcionamento básico do site, incluindo navegação
                                            e acesso a áreas seguras.
                                        </p>
                                    </div>

                                    {/* Cookies Analíticos */}
                                    <div className="bg-cyber-bg/50 p-4 rounded-lg border border-neon-cyan/30">
                                        <div className="flex items-center justify-between mb-3">
                                            <h4 className="text-white font-semibold">Cookies Analíticos</h4>
                                            <label className="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={preferences.analytics}
                                                    onChange={(e) => handlePreferenceChange('analytics', e.target.checked)}
                                                    className="sr-only peer"
                                                />
                                                <div className="w-11 h-6 bg-cyber-surface peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-cyan"></div>
                                            </label>
                                        </div>
                                        <p className="text-cyber-muted text-sm">
                                            Ajudam a entender como os visitantes interagem com o site,
                                            fornecendo informações sobre métricas de uso.
                                        </p>
                                    </div>

                                    {/* Cookies Funcionais */}
                                    <div className="bg-cyber-bg/50 p-4 rounded-lg border border-blue-500/30">
                                        <div className="flex items-center justify-between mb-3">
                                            <h4 className="text-white font-semibold">Cookies Funcionais</h4>
                                            <label className="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={preferences.functional}
                                                    onChange={(e) => handlePreferenceChange('functional', e.target.checked)}
                                                    className="sr-only peer"
                                                />
                                                <div className="w-11 h-6 bg-cyber-surface peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-500"></div>
                                            </label>
                                        </div>
                                        <p className="text-cyber-muted text-sm">
                                            Permitem funcionalidades aprimoradas e personalização, como
                                            preferências de idioma e configurações de tema.
                                        </p>
                                    </div>

                                    {/* Cookies de Marketing */}
                                    <div className="bg-cyber-bg/50 p-4 rounded-lg border border-purple-500/30">
                                        <div className="flex items-center justify-between mb-3">
                                            <h4 className="text-white font-semibold">Cookies de Marketing</h4>
                                            <label className="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={preferences.marketing}
                                                    onChange={(e) => handlePreferenceChange('marketing', e.target.checked)}
                                                    className="sr-only peer"
                                                />
                                                <div className="w-11 h-6 bg-cyber-surface peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                                            </label>
                                        </div>
                                        <p className="text-cyber-muted text-sm">
                                            Usados para rastrear visitantes em sites para exibir anúncios
                                            relevantes e envolventes para o usuário individual.
                                        </p>
                                    </div>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-3 justify-end pt-4 border-t border-cyber-surface">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={handleRejectAll}
                                        className="border-red-500/50 text-red-400 hover:bg-red-500/10 hover:border-red-500"
                                    >
                                        Rejeitar Todos
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={handleSavePreferences}
                                        className="border-neon-cyan/50 text-neon-cyan hover:bg-neon-cyan/10 hover:border-neon-cyan"
                                    >
                                        Salvar Preferências
                                    </Button>
                                    <Button
                                        variant="primary"
                                        size="sm"
                                        onClick={handleAcceptAll}
                                        className="bg-neon-cyan text-cyber-bg hover:bg-neon-cyan/90"
                                    >
                                        Aceitar Todos
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default CookieBanner;