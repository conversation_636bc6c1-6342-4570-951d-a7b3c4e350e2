/**
 * @fileoverview Dropdown de resultados de busca
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Clock, Search, TrendingUp, User, Folder, Tag } from 'lucide-react';
import { useEffect, useRef } from 'react';
import type { BaseComponentProps } from '../../types';
import { useMobileDetection } from '../../hooks/useMobileDetection';

// ============================================================================
// INTERFACES
// ============================================================================

interface SearchResult {
  post: {
    id: string;
    title: string;
    excerpt: string;
    slug: string;
    thumbnail: string;
    author: {
      name: string;
    };
    category: {
      name: string;
      color: string;
    };
    date: string;
    views: number;
    readTime: number;
  };
  relevance: number;
  matchType: 'title' | 'excerpt' | 'author' | 'category' | 'tag';
}

interface SearchDropdownProps extends BaseComponentProps {
  results: SearchResult[];
  loading: boolean;
  query: string;
  isOpen: boolean;
  onClose: () => void;
  onResultClick: (slug: string) => void;
  onViewAll: () => void;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Dropdown com resultados da busca
 */
export const SearchDropdown: React.FC<SearchDropdownProps> = ({
  results,
  loading,
  query,
  isOpen,
  onClose,
  onResultClick,
  onViewAll,
  className
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  // Mobile detection para otimizações
  const { isMobile } = useMobileDetection();

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Não renderizar se não estiver aberto
  if (!isOpen) return null;

  const getMatchIcon = (matchType: SearchResult['matchType']) => {
    switch (matchType) {
      case 'title':
        return <TrendingUp className="w-3 h-3 text-neon-cyan" />;
      case 'author':
        return <User className="w-3 h-3 text-neon-purple" />;
      case 'category':
        return <Folder className="w-3 h-3 text-yellow-400" />;
      case 'tag':
        return <Tag className="w-3 h-3 text-green-400" />;
      default:
        return <Search className="w-3 h-3 text-cyber-muted" />;
    }
  };

  const getMatchLabel = (matchType: SearchResult['matchType']) => {
    switch (matchType) {
      case 'title':
        return 'Título';
      case 'author':
        return 'Autor';
      case 'category':
        return 'Categoria';
      case 'tag':
        return 'Tag';
      default:
        return 'Conteúdo';
    }
  };

  const dropdownClasses = clsx(
    'absolute top-full z-50',
    // Mobile: full width com margin, Desktop: left-0 right-0
    isMobile ? 'left-2 right-2 mt-1' : 'left-0 right-0 mt-2',
    'bg-cyber-surface border border-cyber-border rounded-lg shadow-2xl',
    // Mobile: altura menor
    isMobile ? 'max-h-80' : 'max-h-96',
    'overflow-y-auto',
    'backdrop-blur-md bg-cyber-surface/95',
    className
  );

  return (
    <div ref={dropdownRef} className={dropdownClasses}>
      {/* Loading - Mobile Optimized */}
      {loading && (
        <div className={clsx(
          'text-center',
          'p-3 mobile-xs:p-2 sm:p-4'
        )}>
          <div className="inline-flex items-center space-x-2 text-cyber-muted">
            <div className="w-4 h-4 border-2 border-neon-cyan border-t-transparent rounded-full animate-spin" />
            <span className="text-sm-mobile sm:text-sm">Buscando...</span>
          </div>
        </div>
      )}

      {/* No Results */}
      {!loading && results.length === 0 && query.length >= 2 && (
        <div className="p-4 text-center">
          <Search className="w-8 h-8 text-cyber-muted mx-auto mb-2" />
          <p className="text-cyber-muted text-sm">
            Nenhum resultado encontrado para "{query}"
          </p>
          <p className="text-cyber-muted text-xs mt-1">
            Tente usar termos diferentes ou mais específicos
          </p>
        </div>
      )}

      {/* Results */}
      {!loading && results.length > 0 && (
        <>
          {/* Header */}
          <div className="px-4 py-3 border-b border-cyber-border">
            <div className="flex items-center justify-between">
              <span className="text-sm text-cyber-muted">
                {results.length} resultado{results.length !== 1 ? 's' : ''} para "{query}"
              </span>
              <button
                onClick={onViewAll}
                className="text-xs text-neon-cyan hover:text-neon-cyan/80 transition-colors"
              >
                Ver todos
              </button>
            </div>
          </div>

          {/* Results List */}
          <div className="py-2">
            {results.map((result) => (
              <button
                key={result.post.id}
                onClick={() => onResultClick(result.post.slug)}
                className="w-full px-4 py-3 text-left hover:bg-cyber-surface/50 transition-colors border-b border-cyber-border/30 last:border-b-0"
              >
                <div className="flex items-start space-x-3">
                  {/* Thumbnail */}
                  <div className="flex-shrink-0">
                    <img
                      src={result.post.thumbnail}
                      alt={result.post.title}
                      className="w-12 h-12 rounded-lg object-cover"
                      loading="lazy"
                    />
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    {/* Title */}
                    <h3 className="text-sm font-medium text-cyber-text line-clamp-1 mb-1">
                      {result.post.title}
                    </h3>

                    {/* Excerpt */}
                    <p className="text-xs text-cyber-muted line-clamp-2 mb-2">
                      {result.post.excerpt}
                    </p>

                    {/* Meta */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-xs text-cyber-muted">
                        <span>{result.post.author.name}</span>
                        <span>•</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{result.post.readTime}min</span>
                        </div>
                      </div>

                      {/* Match Type */}
                      <div className="flex items-center space-x-1">
                        {getMatchIcon(result.matchType)}
                        <span className="text-xs text-cyber-muted">
                          {getMatchLabel(result.matchType)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Footer */}
          {results.length >= 5 && (
            <div className="px-4 py-3 border-t border-cyber-border">
              <button
                onClick={onViewAll}
                className="w-full text-center text-sm text-neon-cyan hover:text-neon-cyan/80 transition-colors"
              >
                Ver todos os resultados →
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};
