import React from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { Calendar, Clock, Eye, Heart, MessageCircle, User } from 'lucide-react';
import { Card } from './Card';
import AuthorAvatar from './AuthorAvatar';
import type { Post } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface PostCardProps {
  post: Post;
  variant?: 'default' | 'featured' | 'compact' | 'minimal' | 'hero';
  showAuthor?: boolean;
  showAuthorAvatar?: boolean; // Nova prop para controlar avatar visual
  showDate?: boolean;
  showExcerpt?: boolean;
  showTags?: boolean;
  showViews?: boolean;
  showReadTime?: boolean;
  showInteractions?: boolean;
  className?: string;
  onClick?: (post: Post) => void;
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

const variantConfig = {
  default: {
    container: 'group cursor-pointer',
    image: 'aspect-video',
    content: 'p-6',
    title: 'text-lg font-semibold mb-3',
    excerpt: 'text-sm text-cyber-muted mb-4 line-clamp-3',
  },
  featured: {
    container: 'group cursor-pointer',
    image: 'aspect-[16/10]',
    content: 'p-8',
    title: 'text-xl font-bold mb-4',
    excerpt: 'text-base text-cyber-muted mb-6 line-clamp-4',
  },
  compact: {
    container: 'group cursor-pointer flex space-x-4',
    image: 'w-24 h-24 flex-shrink-0',
    content: 'flex-1 min-w-0',
    title: 'text-base font-semibold mb-2',
    excerpt: 'text-sm text-cyber-muted mb-2 line-clamp-2',
  },
  minimal: {
    container: 'group cursor-pointer',
    image: 'aspect-video',
    content: 'p-4',
    title: 'text-base font-semibold mb-2',
    excerpt: 'text-sm text-cyber-muted mb-3 line-clamp-2',
  },
  hero: {
    container: 'group cursor-pointer relative',
    image: 'aspect-[21/9]',
    content: 'absolute inset-0 bg-gradient-to-t from-cyber-bg/90 via-cyber-bg/50 to-transparent p-8 flex flex-col justify-end',
    title: 'text-2xl font-bold mb-4 text-white',
    excerpt: 'text-lg text-gray-200 mb-6 line-clamp-3',
  },
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const PostCard: React.FC<PostCardProps> = ({
  post,
  variant = 'default',
  showAuthor = true,
  showDate = true,
  showExcerpt = true,
  showTags = false,
  showViews = false,
  showReadTime = true,
  showInteractions = false,
  className,
  onClick,
}) => {
  const config = variantConfig[variant];
  const isCompact = variant === 'compact';
  const isHero = variant === 'hero';

  const handleClick = () => {
    onClick?.(post);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  const renderCategoryBadge = () => (
    <div className={clsx(
      'absolute top-3 left-3 z-10',
      isHero && 'top-6 left-6'
    )}>
      <span
        className={clsx(
          'px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm',
          isHero ? 'text-white bg-white/20' : ''
        )}
        style={!isHero ? {
          backgroundColor: `${post.category.color}20`,
          color: post.category.color,
          border: `1px solid ${post.category.color}30`,
        } : undefined}>
        {post.category.name}
      </span>
    </div>
  );

  const renderFeaturedBadge = () => {
    if (!post.featured) return null;
    
    return (
      <div className={clsx(
        'absolute top-3 right-3 z-10',
        isHero && 'top-6 right-6'
      )}>
        <span className="px-2 py-1 bg-neon-cyan text-cyber-bg rounded text-xs font-bold">
          ⭐ DESTAQUE
        </span>
      </div>
    );
  };

  const renderImage = () => (
    <div className={clsx(
      'bg-gradient-cyber rounded-lg relative overflow-hidden',
      config.image,
      !isCompact && 'mb-4',
      isHero && 'rounded-none'
    )}>
      <img
        src={post.thumbnail}
        alt={post.title}
        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        loading="lazy"
      />
      {renderCategoryBadge()}
      {renderFeaturedBadge()}
      
      {/* Read time overlay para variantes maiores */}
      {showReadTime && !isCompact && (
        <div className="absolute bottom-3 right-3 bg-cyber-bg/80 backdrop-blur-sm px-2 py-1 rounded text-xs text-cyber-muted">
          <Clock className="w-3 h-3 inline mr-1" />
          {post.readTime} min
        </div>
      )}
    </div>
  );

  const renderContent = () => (
    <div className={config.content}>
      {/* Título */}
      <h3 className={clsx(
        config.title,
        'group-hover:text-neon-cyan transition-colors line-clamp-2',
        isHero ? 'text-white' : 'text-cyber-text'
      )}>
        {post.title}
      </h3>

      {/* Excerpt */}
      {showExcerpt && post.excerpt && (
        <p className={config.excerpt}>
          {post.excerpt}
        </p>
      )}

      {/* Tags */}
      {showTags && post.tags && post.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {post.tags.slice(0, 3).map((tag) => (
            <span
              key={tag.id}
              className="px-2 py-1 bg-cyber-surface text-cyber-muted text-xs rounded border border-cyber-border">
              #{tag.name}
            </span>
          ))}
          {post.tags.length > 3 && (
            <span className="text-xs text-cyber-muted">
              +{post.tags.length - 3} mais
            </span>
          )}
        </div>
      )}

      {/* Meta informações */}
      <div className={clsx(
        'flex items-center justify-between text-xs',
        isHero ? 'text-gray-300' : 'text-cyber-muted'
      )}>
        <div className="flex items-center space-x-4">
          {/* Autor */}
          {showAuthor && (
            <div className="flex items-center space-x-2">
              <AuthorAvatar
                avatar={post.author.avatar}
                name={post.author.name}
                size="sm"
              />
              <span>{post.author.name}</span>
            </div>
          )}

          {/* Data */}
          {showDate && (
            <div className="flex items-center space-x-1">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(post.published_at)}</span>
            </div>
          )}

          {/* Read time para compact */}
          {showReadTime && isCompact && (
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{post.readTime} min</span>
            </div>
          )}
        </div>

        {/* Interações */}
        {showInteractions && (
          <div className="flex items-center space-x-3">
            {showViews && (
              <div className="flex items-center space-x-1">
                <Eye className="w-3 h-3" />
                <span>{post.views || 0}</span>
              </div>
            )}
            <div className="flex items-center space-x-1">
              <Heart className="w-3 h-3" />
              <span>{post.likes || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MessageCircle className="w-3 h-3" />
              <span>{post.comments || 0}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Render baseado na variante
  if (isCompact) {
    return (
      <Card
        className={twMerge(clsx(config.container, className))}
        hoverable
        onClick={handleClick}>
        {renderImage()}
        {renderContent()}
      </Card>
    );
  }

  if (isHero) {
    return (
      <div
        className={twMerge(clsx(config.container, 'rounded-lg overflow-hidden', className))}
        onClick={handleClick}>
        {renderImage()}
        {renderContent()}
      </div>
    );
  }

  return (
    <Card
      className={twMerge(clsx(config.container, className))}
      hoverable
      neonBorder={variant === 'featured'}
      padding={false}
      onClick={handleClick}>
      {renderImage()}
      {renderContent()}
    </Card>
  );
};

export default PostCard;
