/**
 * @fileoverview Componente HeroPost - Post principal em destaque com overlay
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';

import { FaArrowRight, FaClock } from 'react-icons/fa';
import { twMerge } from 'tailwind-merge';
import type { BaseComponentProps, Post } from '../../types';
import { Button } from './Button';
import AuthorAvatar from './AuthorAvatar';

// ============================================================================
// INTERFACES
// ============================================================================

interface HeroPostProps extends BaseComponentProps {
  /**
   * Post em destaque
   */
  post: Post;

  /**
   * Altura do hero
   */
  height?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Se deve mostrar overlay gradiente
   */
  showOverlay?: boolean;

  /**
   * Se deve mostrar call-to-action
   */
  showCTA?: boolean;

  /**
   * Texto do botão CTA
   */
  ctaText?: string;

  /**
   * Se deve ter efeito parallax
   */
  parallax?: boolean;

  /**
   * Função de clique no post
   */
  onPostClick?: (post: Post) => void;

  /**
   * Função de clique no CTA
   */
  onCTAClick?: (post: Post) => void;
}

// ============================================================================
// CONSTANTES
// ============================================================================

/**
 * Configurações de altura
 */
const heightConfig = {
  sm: 'h-48 sm:h-64 md:h-80',
  md: 'h-56 sm:h-80 md:h-96',
  lg: 'h-64 sm:h-96 md:h-[32rem]',
  xl: 'h-72 sm:h-[32rem] md:h-[40rem]',
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Post principal em destaque com overlay gradiente e animações
 */
export const HeroPost: React.FC<HeroPostProps> = ({
  post,
  height = 'lg',
  showOverlay = true,
  showCTA = true,
  ctaText = 'Leia mais',
  parallax = true,
  onPostClick,
  onCTAClick,
  className,
  ...props
}) => {
  /**
   * Handle post click
   */
  const handlePostClick = () => {
    onPostClick?.(post);
  };

  /**
   * Handle CTA click
   */
  const handleCTAClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onCTAClick?.(post);
  };

  // Classes do container
  const containerClasses = twMerge(
    clsx(
      'relative overflow-hidden rounded-lg cursor-pointer group',
      'shadow-2xl shadow-cyber-bg/40 hover:shadow-2xl hover:shadow-cyber-bg/60',
      'transition-shadow duration-300',
      heightConfig[height],
      className
    )
  );

  // Classes da imagem
  const imageClasses = clsx(
    'absolute inset-0 w-full h-full object-cover',
    'transition-transform duration-700 ease-out',
    'group-hover:scale-110',
    parallax && 'group-hover:scale-105'
  );

  // Classes do overlay
  const overlayClasses = clsx(
    'absolute inset-0',
    showOverlay && [
      'bg-gradient-to-t from-cyber-bg/90 via-cyber-bg/50 to-transparent',
      'group-hover:from-cyber-bg/95 group-hover:via-cyber-bg/60',
      'transition-all duration-500',
    ]
  );

  return (
    <article className={containerClasses} onClick={handlePostClick} {...props}>
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src={post.thumbnail}
          alt={post.title}
          className={imageClasses}
          loading="lazy"
        />

        {/* Blur overlay para melhor legibilidade */}
        <div className="absolute inset-0 bg-cyber-bg/20 backdrop-blur-[0.5px]" />
      </div>

      {/* Gradient Overlay */}
      {showOverlay && <div className={overlayClasses} />}

      {/* Category Badge */}
      <div className="absolute top-3 left-3 sm:top-6 sm:left-6 z-20">
        <div
          className="px-2 py-1 sm:px-3 sm:py-1 rounded-full text-xs font-bold backdrop-blur-sm border"
          style={{
            backgroundColor: `${post.category.color}20`,
            color: post.category.color,
            borderColor: `${post.category.color}40`,
          }}>
          <span className="sm:hidden">{post.category.name.charAt(0)}</span>
          <span className="hidden sm:inline">{post.category.name}</span>
        </div>
      </div>

      {/* Featured Badge */}
      <div className="absolute top-3 right-3 sm:top-6 sm:right-6 z-20">
        <div className="bg-neon-cyan text-cyber-bg px-2 py-1 sm:px-3 sm:py-1 rounded-full text-xs font-bold">
          <span className="sm:hidden">⭐</span>
          <span className="hidden sm:inline">⭐ DESTAQUE</span>
        </div>
      </div>

      {/* Content */}
      <div className="absolute inset-0 flex flex-col justify-end p-4 sm:p-6 md:p-8 z-10">
        <div className="space-y-2 sm:space-y-4">
          {/* Title */}
          <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold text-white leading-tight group-hover:text-neon-cyan transition-colors duration-300">
            {post.title}
          </h1>

          {/* Excerpt */}
          {post.excerpt && (
            <p className="text-gray-200 text-xs sm:text-sm md:text-base line-clamp-2 max-w-2xl">
              {post.excerpt}
            </p>
          )}

          {/* Meta Info */}
          <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-300">
            <div className="flex items-center gap-1 sm:gap-2">
              <AuthorAvatar
                avatar={post.author.avatar}
                name={post.author.name}
                size="sm"
                className="w-4 h-4 sm:w-5 sm:h-5"
              />
              <span className="truncate">{post.author.name}</span>
            </div>

            <div className="flex items-center gap-1 sm:gap-2">
              <FaClock className="text-neon-cyan text-xs sm:text-sm" />
              <span className="hidden sm:inline">
                {post.readTime} min de leitura
              </span>
              <span className="sm:hidden">{post.readTime}min</span>
            </div>

            <div className="hidden sm:flex items-center gap-2">
              <span className="text-neon-cyan">📅</span>
              <span>{new Date(post.date).toLocaleDateString('pt-BR')}</span>
            </div>
          </div>

          {/* CTA Button */}
          {showCTA && (
            <div className="pt-1 sm:pt-2">
              <Button
                variant="primary"
                size="sm"
                onClick={handleCTAClick}
                className="group/btn text-xs sm:text-sm">
                <span>{ctaText}</span>
                <FaArrowRight className="ml-1 sm:ml-2 group-hover/btn:translate-x-1 transition-transform duration-300 text-xs sm:text-sm" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </article>
  );
};

// ============================================================================
// COMPONENTES AUXILIARES
// ============================================================================

/**
 * Skeleton loader para HeroPost
 */
export const HeroPostSkeleton: React.FC<{
  height?: 'sm' | 'md' | 'lg' | 'xl';
}> = ({ height = 'lg' }) => (
  <div
    className={clsx(
      'relative overflow-hidden rounded-lg animate-pulse',
      heightConfig[height]
    )}>
    <div className="absolute inset-0 bg-cyber-surface" />
    <div className="absolute inset-0 flex flex-col justify-end p-6 md:p-8">
      <div className="space-y-4">
        <div className="h-8 bg-cyber-border rounded w-3/4" />
        <div className="h-4 bg-cyber-border rounded w-1/2" />
        <div className="flex gap-4">
          <div className="h-4 bg-cyber-border rounded w-20" />
          <div className="h-4 bg-cyber-border rounded w-24" />
        </div>
        <div className="h-10 bg-cyber-border rounded w-32" />
      </div>
    </div>
  </div>
);

export default HeroPost;
