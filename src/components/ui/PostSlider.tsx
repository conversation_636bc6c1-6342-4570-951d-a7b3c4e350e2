/**
 * @fileoverview Componente PostSlider - Carrossel horizontal de posts
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useRef, useState } from 'react';
import type { Post } from '../../types';
import { Button } from './';
import AuthorAvatar from './AuthorAvatar';

// ============================================================================
// INTERFACES
// ============================================================================

interface PostSliderProps {
  className?: string;
  title?: string;
  subtitle?: string;
  posts?: Post[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  slidesToShow?: number;
  gap?: number;
  onPostClick?: (post: Post) => void;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Carrossel horizontal de posts com navegação
 */
export const PostSlider: React.FC<PostSliderProps> = ({
  className,
  title = '📚 Posts Relacionados',
  subtitle = 'Continue lendo',
  posts = [],
  autoPlay = false,
  autoPlayInterval = 5000,
  showDots = true,
  showArrows = true,
  slidesToShow = 3,
  gap = 24,
  onPostClick,
}) => {
  // Mobile detection já aplicado no layout responsivo

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [currentSlidesToShow, setCurrentSlidesToShow] = useState(1);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const sliderRef = useRef<HTMLDivElement>(null);
  const autoPlayRef = useRef<number | null>(null);

  // Responsividade: ajusta slidesToShow e gap baseado no tamanho da tela
  const [currentGap, setCurrentGap] = useState(16);

  useEffect(() => {
    const updateSlidesToShow = () => {
      const width = window.innerWidth;
      if (width < 640) {
        // Mobile: 1 slide, gap menor
        setCurrentSlidesToShow(1);
        setCurrentGap(16);
      } else if (width < 1024) {
        // Tablet: 2 slides, gap médio
        setCurrentSlidesToShow(2);
        setCurrentGap(20);
      } else {
        // Desktop: usar prop ou padrão 3, gap original
        setCurrentSlidesToShow(slidesToShow);
        setCurrentGap(gap);
      }
    };

    updateSlidesToShow();
    window.addEventListener('resize', updateSlidesToShow);
    return () => window.removeEventListener('resize', updateSlidesToShow);
  }, [slidesToShow, gap]);

  // Calcula quantos slides são possíveis
  const totalSlides = Math.max(0, posts.length - currentSlidesToShow + 1);
  const canSlide = posts.length > currentSlidesToShow;

  // Auto-play
  useEffect(() => {
    if (autoPlay && canSlide && !isHovered) {
      autoPlayRef.current = window.setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % totalSlides);
      }, autoPlayInterval);
    }

    return () => {
      if (autoPlayRef.current) {
        window.clearInterval(autoPlayRef.current);
      }
    };
  }, [autoPlay, canSlide, isHovered, totalSlides, autoPlayInterval]);

  // Navegação
  const goToSlide = (index: number) => {
    setCurrentSlide(Math.max(0, Math.min(index, totalSlides - 1)));
  };

  const nextSlide = () => {
    if (currentSlide < totalSlides - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  // Touch/Swipe navigation
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && currentSlide < totalSlides - 1) {
      nextSlide();
    }
    if (isRightSwipe && currentSlide > 0) {
      prevSlide();
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') prevSlide();
      if (e.key === 'ArrowRight') nextSlide();
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentSlide, totalSlides]);

  if (posts.length === 0) {
    return null;
  }

  return (
    <div className={clsx('space-y-4 mobile-xs:space-y-3 sm:space-y-6', className)}>
      {/* Header - Mobile Optimized */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl-mobile sm:text-xl lg:text-2xl font-bold text-cyber-text">
            {title}
          </h2>
          {subtitle && (
            <p className="text-sm-mobile sm:text-sm lg:text-base text-cyber-muted">{subtitle}</p>
          )}
        </div>

        {/* Navigation Arrows - Hidden on mobile */}
        {showArrows && canSlide && (
          <div className="hidden sm:flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={prevSlide}
              disabled={currentSlide === 0}
              className="w-10 h-10 p-0">
              ←
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={nextSlide}
              disabled={currentSlide >= totalSlides - 1}
              className="w-10 h-10 p-0">
              →
            </Button>
          </div>
        )}
      </div>

      {/* Slider Container */}
      <div
        className="relative overflow-hidden"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}>
        <div
          ref={sliderRef}
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${
              currentSlide * (100 / currentSlidesToShow)
            }%)`,
            gap: `${currentGap}px`,
          }}>
          {posts.map((post) => (
            <div
              key={post.id}
              className="flex-shrink-0"
              style={{
                width: `calc(${100 / currentSlidesToShow}% - ${
                  (currentGap * (currentSlidesToShow - 1)) / currentSlidesToShow
                }px)`,
              }}>
              {/* Hero-style Card */}
              <div
                className="relative h-64 sm:h-72 lg:h-80 rounded-lg overflow-hidden cursor-pointer group shadow-2xl shadow-cyber-bg/40 hover:shadow-2xl hover:shadow-cyber-bg/60 transition-shadow duration-300"
                onClick={() => onPostClick?.(post)}>
                {/* Background Image */}
                <div className="absolute inset-0">
                  <img
                    src={post.thumbnail}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {/* Blur overlay para melhor legibilidade */}
                  <div className="absolute inset-0 bg-cyber-bg/20 backdrop-blur-[0.5px]" />
                </div>

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-cyber-bg/90 via-cyber-bg/20 to-transparent" />

                {/* Category Badge */}
                <div className="absolute top-2 left-2 sm:top-4 sm:left-4 z-10">
                  <span
                    className="px-1.5 py-0.5 sm:px-2 sm:py-1 rounded text-xs font-medium"
                    style={{
                      backgroundColor: `${post.category.color}20`,
                      color: post.category.color,
                      border: `1px solid ${post.category.color}30`,
                    }}>
                    {post.category.name}
                  </span>
                </div>

                {/* Read Time */}
                <div className="absolute top-2 right-2 sm:top-4 sm:right-4 z-10">
                  <div className="bg-cyber-bg/80 backdrop-blur-sm px-1.5 py-0.5 sm:px-2 sm:py-1 rounded text-xs text-cyber-muted">
                    {post.readTime} min
                  </div>
                </div>

                {/* Content */}
                <div className="absolute inset-0 flex flex-col justify-end p-3 sm:p-4 z-10">
                  <div className="space-y-1 sm:space-y-2">
                    {/* Title */}
                    <h3 className="text-sm sm:text-base lg:text-lg font-bold text-white leading-tight group-hover:text-neon-cyan transition-colors line-clamp-2">
                      {post.title}
                    </h3>

                    {/* Meta Info */}
                    <div className="flex items-center gap-2 sm:gap-3 text-xs text-gray-300">
                      <div className="flex items-center gap-1">
                        <AuthorAvatar
                          avatar={post.author.avatar}
                          name={post.author.name}
                          size="sm"
                          className="w-3 h-3 sm:w-4 sm:h-4"
                        />
                        <span className="truncate">{post.author.name}</span>
                      </div>
                      <span className="hidden sm:inline">•</span>
                      <span className="hidden sm:inline">
                        {new Date(post.date).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Dots Navigation */}
      {showDots && canSlide && (
        <div className="flex justify-center space-x-2">
          {Array.from({ length: totalSlides }, (_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={clsx(
                'w-2 h-2 rounded-full transition-all duration-200',
                currentSlide === index
                  ? 'bg-neon-cyan w-6'
                  : 'bg-cyber-border hover:bg-cyber-muted'
              )}
              aria-label={`Ir para slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Progress Bar (opcional) */}
      {autoPlay && canSlide && (
        <div className="w-full bg-cyber-border rounded-full h-1 overflow-hidden">
          <div
            className="h-full bg-gradient-neon transition-all duration-100 ease-linear"
            style={{
              width: `${((currentSlide + 1) / totalSlides) * 100}%`,
            }}
          />
        </div>
      )}

      {/* Mobile Touch Indicators */}
      <div className="md:hidden text-center text-xs text-cyber-muted">
        Deslize para navegar • {currentSlide + 1} de {totalSlides}
      </div>
    </div>
  );
};

export default PostSlider;
