/**
 * @fileoverview Componente de Breadcrumbs para navegação e SEO
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Link } from 'react-router';
import { useMobileDetection } from '../../hooks/useMobileDetection';

// ============================================================================
// INTERFACES
// ============================================================================

interface BreadcrumbItem {
  name: string;
  url: string;
  isActive?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  separator?: React.ReactNode;
  showHome?: boolean;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Componente de breadcrumbs para navegação hierárquica
 */
export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className,
  separator = '/',
  showHome = true
}) => {
  // Mobile detection para otimizações
  const { isMobile } = useMobileDetection();

  // Adicionar Home se não estiver presente e showHome for true
  const breadcrumbItems = showHome && items[0]?.name !== 'Home'
    ? [{ name: 'Home', url: '/' }, ...items]
    : items;

  if (breadcrumbItems.length <= 1) {
    return null; // Não mostrar breadcrumbs se houver apenas um item
  }

  return (
    <nav
      className={clsx(
        'flex items-center text-sm-mobile sm:text-sm',
        // Mobile: scroll horizontal se necessário
        'overflow-x-auto scrollbar-hide',
        'space-x-1.5 mobile-xs:space-x-1 sm:space-x-2',
        className
      )}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-1.5 mobile-xs:space-x-1 sm:space-x-2 whitespace-nowrap">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          const isActive = item.isActive || isLast;

          return (
            <li key={`${item.url}-${index}`} className="flex items-center space-x-1.5 mobile-xs:space-x-1 sm:space-x-2 flex-shrink-0">
              {/* Breadcrumb Item */}
              {isActive ? (
                <span
                  className={clsx(
                    'text-cyber-text font-medium',
                    // Mobile: truncar se necessário
                    isMobile && 'truncate max-w-[120px] mobile-xs:max-w-[80px]'
                  )}
                  aria-current="page"
                  title={item.name}
                >
                  {item.name}
                </span>
              ) : (
                <Link
                  to={item.url}
                  className={clsx(
                    'text-cyber-muted hover:text-neon-cyan transition-colors duration-200',
                    // Mobile: truncar se necessário
                    isMobile && 'truncate max-w-[120px] mobile-xs:max-w-[80px]'
                  )}
                  title={item.name}
                >
                  {item.name}
                </Link>
              )}

              {/* Separator */}
              {!isLast && (
                <span
                  className="text-cyber-muted/60 select-none flex-shrink-0"
                  aria-hidden="true"
                >
                  {separator}
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

// ============================================================================
// VARIANTES
// ============================================================================

/**
 * Breadcrumbs com estilo cyberpunk
 */
export const CyberBreadcrumbs: React.FC<BreadcrumbsProps> = (props) => {
  return (
    <Breadcrumbs
      {...props}
      className={clsx(
        'bg-cyber-surface/50 backdrop-blur-sm border border-cyber-border/50 rounded-lg px-4 py-2',
        props.className
      )}
      separator={
        <svg 
          className="w-3 h-3 text-neon-cyan/60" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M9 5l7 7-7 7" 
          />
        </svg>
      }
    />
  );
};

/**
 * Breadcrumbs minimalista
 */
export const MinimalBreadcrumbs: React.FC<BreadcrumbsProps> = (props) => {
  return (
    <Breadcrumbs
      {...props}
      className={clsx('text-xs', props.className)}
      separator="•"
    />
  );
};

/**
 * Breadcrumbs com ícones
 */
export const IconBreadcrumbs: React.FC<BreadcrumbsProps> = (props) => {
  return (
    <Breadcrumbs
      {...props}
      separator={
        <svg 
          className="w-4 h-4 text-cyber-muted/40" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={1.5} 
            d="M13 7l5 5-5 5M6 12h12" 
          />
        </svg>
      }
    />
  );
};

export default Breadcrumbs;
