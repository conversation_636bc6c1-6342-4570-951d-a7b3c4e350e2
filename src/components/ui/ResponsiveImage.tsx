/**
 * @fileoverview Componente de Imagem Responsiva Otimizada para Mobile
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState, useRef, useEffect } from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// ============================================================================
// INTERFACES
// ============================================================================

interface ResponsiveImageProps {
  /** URL da imagem principal */
  src: string;
  
  /** Texto alternativo */
  alt: string;
  
  /** Tamanhos responsivos para srcset */
  sizes?: {
    mobile: string;    // 320px, 480px, 640px
    tablet: string;    // 768px, 1024px
    desktop: string;   // 1280px, 1920px
  };
  
  /** Breakpoints para sizes attribute */
  breakpoints?: string;
  
  /** Formato preferido (WebP com fallback) */
  format?: 'webp' | 'jpeg' | 'png';
  
  /** Qualidade da imagem (1-100) */
  quality?: number;
  
  /** Se deve usar lazy loading */
  lazy?: boolean;
  
  /** Placeholder durante carregamento */
  placeholder?: 'blur' | 'skeleton' | 'none';
  
  /** Cor do placeholder */
  placeholderColor?: string;
  
  /** Aspect ratio */
  aspectRatio?: 'square' | 'video' | 'wide' | 'portrait' | string;
  
  /** Classes CSS adicionais */
  className?: string;
  
  /** Callback quando imagem carrega */
  onLoad?: () => void;
  
  /** Callback quando há erro */
  onError?: () => void;
  
  /** Props adicionais para img */
  imgProps?: React.ImgHTMLAttributes<HTMLImageElement>;
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

const aspectRatioConfig = {
  square: 'aspect-square',
  video: 'aspect-video',
  wide: 'aspect-[21/9]',
  portrait: 'aspect-[3/4]',
};

const placeholderConfig = {
  blur: 'bg-gradient-to-br from-cyber-surface to-cyber-border animate-pulse',
  skeleton: 'bg-cyber-surface animate-pulse',
  none: 'bg-transparent',
};

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Gera URLs otimizadas para diferentes tamanhos
 */
const generateSrcSet = (
  src: string,
  _sizes: { mobile: string; tablet: string; desktop: string },
  format: string = 'webp',
  quality: number = 80
): string => {
  const mobileSizes = [320, 480, 640];
  const tabletSizes = [768, 1024];
  const desktopSizes = [1280, 1920];
  
  const allSizes = [...mobileSizes, ...tabletSizes, ...desktopSizes];
  
  return allSizes
    .map(size => {
      // Se a URL já contém parâmetros, adiciona com &, senão com ?
      const separator = src.includes('?') ? '&' : '?';
      return `${src}${separator}w=${size}&q=${quality}&f=${format} ${size}w`;
    })
    .join(', ');
};

/**
 * Gera o atributo sizes baseado nos breakpoints
 */
const generateSizes = (breakpoints?: string): string => {
  if (breakpoints) return breakpoints;
  
  return [
    '(max-width: 640px) 100vw',
    '(max-width: 1024px) 50vw',
    '33vw'
  ].join(', ');
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Componente de imagem responsiva otimizada para performance mobile
 */
export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  sizes,
  breakpoints,
  format = 'webp',
  quality = 80,
  lazy = true,
  placeholder = 'blur',
  placeholderColor = 'from-cyber-surface to-cyber-border',
  aspectRatio = 'video',
  className,
  onLoad,
  onError,
  imgProps,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer para lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // Carrega 50px antes de entrar na viewport
        threshold: 0.1,
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // Handlers
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Classes
  const containerClasses = twMerge(
    clsx(
      'relative overflow-hidden',
      typeof aspectRatio === 'string' && aspectRatioConfig[aspectRatio as keyof typeof aspectRatioConfig]
        ? aspectRatioConfig[aspectRatio as keyof typeof aspectRatioConfig]
        : aspectRatio,
      className
    )
  );

  const imageClasses = clsx(
    'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
    isLoaded ? 'opacity-100' : 'opacity-0'
  );

  const placeholderClasses = clsx(
    'absolute inset-0 w-full h-full',
    placeholderConfig[placeholder],
    placeholderColor && placeholder === 'blur' && placeholderColor,
    isLoaded && 'opacity-0'
  );

  // Gerar srcset e sizes se fornecidos
  const srcSet = sizes ? generateSrcSet(src, sizes, format, quality) : undefined;
  const sizesAttr = generateSizes(breakpoints);

  return (
    <div ref={containerRef} className={containerClasses}>
      {/* Placeholder */}
      <div className={placeholderClasses}>
        {placeholder === 'skeleton' && (
          <div className="flex items-center justify-center h-full">
            <div className="w-8 h-8 border-2 border-cyber-border border-t-neon-cyan rounded-full animate-spin" />
          </div>
        )}
      </div>

      {/* Imagem principal */}
      {isInView && !hasError && (
        <picture>
          {/* WebP source */}
          {format === 'webp' && srcSet && (
            <source
              srcSet={srcSet}
              sizes={sizesAttr}
              type="image/webp"
            />
          )}
          
          {/* Fallback image */}
          <img
            ref={imgRef}
            src={src}
            alt={alt}
            srcSet={srcSet}
            sizes={sizesAttr}
            className={imageClasses}
            loading={lazy ? 'lazy' : 'eager'}
            decoding="async"
            onLoad={handleLoad}
            onError={handleError}
            {...imgProps}
          />
        </picture>
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-cyber-surface text-cyber-muted">
          <div className="text-center">
            <div className="text-2xl mb-2">🖼️</div>
            <div className="text-sm">Erro ao carregar imagem</div>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {!isLoaded && !hasError && isInView && (
        <div className="absolute top-2 right-2 w-4 h-4 border border-neon-cyan border-t-transparent rounded-full animate-spin opacity-50" />
      )}
    </div>
  );
};

// ============================================================================
// VARIAÇÕES ESPECIAIS
// ============================================================================

/**
 * Imagem de avatar responsiva
 */
export const ResponsiveAvatar: React.FC<Omit<ResponsiveImageProps, 'aspectRatio'> & { size?: 'sm' | 'md' | 'lg' | 'xl' }> = ({
  size = 'md',
  className,
  ...props
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
  };

  return (
    <ResponsiveImage
      {...props}
      aspectRatio="square"
      className={twMerge(clsx(sizeClasses[size], 'rounded-full', className))}
    />
  );
};

/**
 * Imagem de thumbnail responsiva
 */
export const ResponsiveThumbnail: React.FC<ResponsiveImageProps> = ({
  className,
  ...props
}) => {
  return (
    <ResponsiveImage
      {...props}
      aspectRatio="video"
      className={twMerge(clsx('rounded-lg', className))}
    />
  );
};

export default ResponsiveImage;
