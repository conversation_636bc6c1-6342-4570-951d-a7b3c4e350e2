/**
 * @fileoverview Exports dos componentes UI base
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

// Componentes base
export {
  AnimatedWrapper,
  BlurIn,
  FadeIn,
  RotateIn,
  ScaleIn,
  SlideIn,
  StaggerContainer,
  StaggerItem,
  useAnimation,
  useScrollAnimation,
} from './AnimatedWrapper';
export { BackToTop, useIsAtTop, useSmoothScroll } from './BackToTop';
export {
  Breadcrumbs,
  CyberBreadcrumbs,
  IconBreadcrumbs,
  MinimalBreadcrumbs,
} from './Breadcrumbs';
export { BreakingNews } from './BreakingNews';
export { Button } from './Button';
export { Card, CardContent, CardFooter, CardHeader } from './Card';
export { FeaturedPosts } from './FeaturedPosts';
export { HeroPost, HeroPostSkeleton } from './HeroPost';
export { Input, Textarea } from './Input';
export { LogoNeon } from './LogoNeon';
export { <PERSON>dal, ModalBody, ModalFooter, ModalHeader } from './Modal';
export { PostSlider } from './PostSlider';
export { SearchDropdown } from './SearchDropdown';
export { Sidebar } from './Sidebar';
export { TopOfMonth, TopOfMonthSkeleton } from './TopOfMonth';

// Re-exports para facilitar importação
export type { BaseComponentProps } from '../../types';
