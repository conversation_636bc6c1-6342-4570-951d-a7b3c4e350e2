/**
 * @fileoverview Componente Sidebar com widgets do blog
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useCategories } from '../../hooks/useCategories';
import { usePosts } from '../../hooks/usePosts';
import { useTags } from '../../hooks/useTags';
import { Button, Card, CardContent, CardHeader } from './';

// ============================================================================
// INTERFACES
// ============================================================================

interface SidebarProps {
  className?: string;
  showTopPosts?: boolean;
  showCategories?: boolean;
  showStats?: boolean;
  showNewsletter?: boolean;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Sidebar com widgets informativos do blog
 */
export const Sidebar: React.FC<SidebarProps> = ({
  className,
  showTopPosts = true,
  showCategories = true,
  showStats = true,
  showNewsletter = true,
}) => {
  // Hooks para carregar dados reais do Supabase
  const { posts: allPosts } = usePosts({
    status: 'published',
    orderBy: 'views',
    orderDirection: 'desc',
    limit: 50, // Carrega mais posts para calcular estatísticas
  });

  const { categories } = useCategories();
  const { tags } = useTags();

  // Top posts (mais visualizados)
  const topPosts = allPosts.slice(0, 5);

  // Estatísticas calculadas dos dados reais
  const stats = {
    totalPosts: allPosts.length,
    totalViews: allPosts.reduce((acc, post) => acc + post.views, 0),
    totalLikes: allPosts.reduce((acc, post) => acc + post.likes, 0),
    totalAuthors: <AUTHORS>
  };

  // Tags populares (primeiras 12 tags)
  const popularTags = tags.slice(0, 12);

  return (
    <div className={clsx('space-y-6', className)}>
      {/* Top Posts do Mês */}
      {showTopPosts && (
        <Card>
          <CardHeader
            title="🔥 Top do Mês"
            subtitle="Posts mais visualizados"
          />
          <CardContent>
            <div className="space-y-4">
              {topPosts.map((post, index) => (
                <div key={post.id} className="flex items-start space-x-3 group">
                  <div className="flex-shrink-0">
                    <span
                      className={clsx(
                        'inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold',
                        index === 0 && 'bg-neon-yellow text-cyber-bg',
                        index === 1 && 'bg-gray-400 text-cyber-bg',
                        index === 2 && 'bg-orange-500 text-cyber-bg',
                        index > 2 && 'bg-cyber-surface text-cyber-muted'
                      )}>
                      {index + 1}
                    </span>
                  </div>

                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-cyber-text group-hover:text-neon-cyan transition-colors line-clamp-2 cursor-pointer">
                      {post.title}
                    </h4>
                    <div className="flex items-center space-x-3 mt-1 text-xs text-cyber-muted">
                      <span className="flex items-center space-x-1">
                        <span>👁️</span>
                        <span>{post.views}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <span>❤️</span>
                        <span>{post.likes}</span>
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Categorias */}
      {showCategories && (
        <Card>
          <CardHeader title="📂 Categorias" subtitle="Explore por tópico" />
          <CardContent>
            <div className="space-y-2">
              {categories.map((category) => (
                <a
                  key={category.id}
                  href={`/categories/${category.slug}`}
                  className="flex items-center justify-between p-2 rounded-lg hover:bg-cyber-surface transition-colors group">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="text-sm text-cyber-text group-hover:text-neon-cyan transition-colors">
                      {category.name}
                    </span>
                  </div>
                  <span className="text-xs text-cyber-muted bg-cyber-bg px-2 py-1 rounded">
                    {category.postsCount}
                  </span>
                </a>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Estatísticas */}
      {showStats && (
        <Card>
          <CardHeader title="📊 Estatísticas" subtitle="Números do blog" />
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-neon-cyan">
                  {stats.totalPosts}
                </div>
                <div className="text-xs text-cyber-muted">Posts</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-neon-magenta">
                  {stats.totalViews.toLocaleString()}
                </div>
                <div className="text-xs text-cyber-muted">Views</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-neon-purple">
                  {stats.totalLikes}
                </div>
                <div className="text-xs text-cyber-muted">Likes</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-neon-green">
                  {stats.totalAuthors}
                </div>
                <div className="text-xs text-cyber-muted">Autores</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Newsletter */}
      {showNewsletter && (
        <Card className="bg-gradient-cyber">
          <CardHeader title="📧 Newsletter" subtitle="Receba novidades" />
          <CardContent>
            <p className="text-sm text-cyber-muted mb-4">
              Fique por dentro das últimas novidades em tecnologia e
              desenvolvimento.
            </p>

            <div className="space-y-3">
              <input
                type="email"
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 rounded-lg bg-cyber-bg border border-cyber-border text-cyber-text placeholder:text-cyber-muted focus:outline-none focus:border-neon-cyan text-sm"
              />
              <Button size="sm" fullWidth>
                Inscrever-se
              </Button>
            </div>

            <p className="text-xs text-cyber-muted mt-3">
              Sem spam. Cancele a qualquer momento.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Social Links */}
      <Card>
        <CardHeader title="🌐 Siga-nos" subtitle="Redes sociais" />
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            {[
              { name: 'Twitter', icon: '🐦', color: 'text-blue-400' },
              { name: 'LinkedIn', icon: '💼', color: 'text-blue-600' },
              { name: 'GitHub', icon: '🐙', color: 'text-gray-400' },
              { name: 'YouTube', icon: '📺', color: 'text-red-500' },
            ].map((social) => (
              <a
                key={social.name}
                href="#"
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-cyber-surface transition-colors group">
                <span className="text-lg">{social.icon}</span>
                <span
                  className={clsx(
                    'text-sm font-medium transition-colors',
                    'text-cyber-muted group-hover:text-cyber-text'
                  )}>
                  {social.name}
                </span>
              </a>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tags Populares */}
      <Card>
        <CardHeader title="🏷️ Tags Populares" subtitle="Tópicos em alta" />
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {popularTags.length > 0
              ? popularTags.map((tag) => (
                  <a
                    key={tag.id}
                    href={`/tag/${tag.slug}`}
                    className="px-2 py-1 rounded text-xs bg-cyber-bg text-cyber-muted hover:text-neon-cyan border border-cyber-border hover:border-neon-cyan transition-colors"
                    style={{
                      borderColor: `${tag.color}30`,
                      color: tag.color,
                    }}>
                    #{tag.name}
                  </a>
                ))
              : // Fallback se não houver tags
                [
                  'React',
                  'TypeScript',
                  'Next.js',
                  'Tailwind',
                  'Node.js',
                  'Python',
                ].map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 rounded text-xs bg-cyber-bg text-cyber-muted border border-cyber-border">
                    #{tag}
                  </span>
                ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Sidebar;
