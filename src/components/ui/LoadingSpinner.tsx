import React from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// ============================================================================
// INTERFACES
// ============================================================================

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'white' | 'neon';
  className?: string;
  text?: string;
  showText?: boolean;
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

const sizeConfig = {
  sm: {
    spinner: 'w-4 h-4',
    text: 'text-xs-mobile sm:text-xs',
    gap: 'gap-2',
  },
  md: {
    spinner: 'w-6 h-6',
    text: 'text-sm-mobile sm:text-sm',
    gap: 'gap-2',
  },
  lg: {
    spinner: 'w-8 h-8',
    text: 'text-base-mobile sm:text-base',
    gap: 'gap-3',
  },
  xl: {
    spinner: 'w-12 h-12',
    text: 'text-lg-mobile sm:text-lg',
    gap: 'gap-4',
  },
};

const colorConfig = {
  primary: {
    border: 'border-neon-cyan',
    borderTop: 'border-t-transparent',
    text: 'text-neon-cyan',
  },
  secondary: {
    border: 'border-cyber-muted',
    borderTop: 'border-t-transparent',
    text: 'text-cyber-muted',
  },
  white: {
    border: 'border-white',
    borderTop: 'border-t-transparent',
    text: 'text-white',
  },
  neon: {
    border: 'border-neon-magenta',
    borderTop: 'border-t-neon-cyan',
    text: 'text-neon-cyan',
  },
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Spinner de loading com múltiplas variações
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className,
  text = 'Carregando...',
  showText = false,
}) => {
  // Mobile detection já aplicado no sizeConfig

  const sizeStyles = sizeConfig[size];
  const colorStyles = colorConfig[color];

  const spinnerClasses = twMerge(
    clsx(
      'border-2 rounded-full animate-spin',
      sizeStyles.spinner,
      colorStyles.border,
      colorStyles.borderTop
    )
  );

  const containerClasses = twMerge(
    clsx(
      'flex items-center justify-center',
      showText && sizeStyles.gap,
      className
    )
  );

  const textClasses = clsx(
    sizeStyles.text,
    colorStyles.text,
    'font-medium'
  );

  if (showText) {
    return (
      <div className={containerClasses}>
        <div className={spinnerClasses} />
        <span className={textClasses}>{text}</span>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      <div className={spinnerClasses} />
    </div>
  );
};

// ============================================================================
// VARIAÇÕES ESPECIAIS
// ============================================================================

/**
 * Spinner com efeito neon duplo
 */
export const NeonLoadingSpinner: React.FC<Omit<LoadingSpinnerProps, 'color'>> = ({
  size = 'md',
  className,
  text = 'Carregando...',
  showText = false,
}) => {
  const sizeStyles = sizeConfig[size];

  const containerClasses = twMerge(
    clsx(
      'flex items-center justify-center',
      showText && sizeStyles.gap,
      className
    )
  );

  return (
    <div className={containerClasses}>
      <div className="relative">
        {/* Spinner externo */}
        <div className={clsx(
          'border-2 border-neon-cyan/30 border-t-neon-cyan rounded-full animate-spin',
          sizeStyles.spinner
        )} />
        
        {/* Spinner interno */}
        <div className={clsx(
          'absolute inset-0 border-2 border-transparent border-r-neon-magenta rounded-full animate-spin animate-reverse',
          sizeStyles.spinner
        )} />
      </div>
      
      {showText && (
        <span className={clsx(sizeStyles.text, 'text-neon-cyan font-medium')}>
          {text}
        </span>
      )}
    </div>
  );
};

/**
 * Spinner com pontos pulsantes
 */
export const DotsLoadingSpinner: React.FC<Omit<LoadingSpinnerProps, 'color'>> = ({
  size = 'md',
  className,
  text = 'Carregando...',
  showText = false,
}) => {
  const sizeStyles = sizeConfig[size];
  
  const dotSize = {
    sm: 'w-1 h-1',
    md: 'w-1.5 h-1.5',
    lg: 'w-2 h-2',
    xl: 'w-3 h-3',
  }[size];

  const containerClasses = twMerge(
    clsx(
      'flex items-center justify-center',
      showText && sizeStyles.gap,
      className
    )
  );

  return (
    <div className={containerClasses}>
      <div className="flex space-x-1">
        <div className={clsx(dotSize, 'bg-neon-cyan rounded-full animate-pulse')} 
             style={{ animationDelay: '0ms' }} />
        <div className={clsx(dotSize, 'bg-neon-magenta rounded-full animate-pulse')} 
             style={{ animationDelay: '150ms' }} />
        <div className={clsx(dotSize, 'bg-neon-purple rounded-full animate-pulse')} 
             style={{ animationDelay: '300ms' }} />
      </div>
      
      {showText && (
        <span className={clsx(sizeStyles.text, 'text-cyber-text font-medium')}>
          {text}
        </span>
      )}
    </div>
  );
};

export default LoadingSpinner;
