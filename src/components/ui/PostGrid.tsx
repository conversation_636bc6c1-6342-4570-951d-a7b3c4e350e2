import React from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { PostCard } from './PostCard';
import { LoadingSpinner } from './LoadingSpinner';
import type { Post } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface PostGridProps {
  posts: Post[];
  loading?: boolean;
  error?: string | null;
  layout?: 'grid' | 'masonry' | 'list' | 'featured';
  columns?: 1 | 2 | 3 | 4;
  showAuthor?: boolean;
  showDate?: boolean;
  showExcerpt?: boolean;
  showTags?: boolean;
  showViews?: boolean;
  showReadTime?: boolean;
  showInteractions?: boolean;
  onPostClick?: (post: Post) => void;
  className?: string;
  emptyMessage?: string;
  emptyIcon?: string;
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

const layoutConfig = {
  grid: {
    container: 'grid gap-6',
    columns: {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    },
    cardVariant: 'default' as const,
  },
  masonry: {
    container: 'columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6',
    columns: {
      1: 'columns-1',
      2: 'columns-1 md:columns-2',
      3: 'columns-1 md:columns-2 lg:columns-3',
      4: 'columns-1 md:columns-2 lg:columns-3 xl:columns-4',
    },
    cardVariant: 'minimal' as const,
  },
  list: {
    container: 'space-y-4',
    columns: {
      1: '',
      2: '',
      3: '',
      4: '',
    },
    cardVariant: 'compact' as const,
  },
  featured: {
    container: 'grid gap-6',
    columns: {
      1: 'grid-cols-1',
      2: 'grid-cols-1 lg:grid-cols-2',
      3: 'grid-cols-1 lg:grid-cols-3',
      4: 'grid-cols-1 lg:grid-cols-2 xl:grid-cols-4',
    },
    cardVariant: 'featured' as const,
  },
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export const PostGrid: React.FC<PostGridProps> = ({
  posts,
  loading = false,
  error = null,
  layout = 'grid',
  columns = 3,
  showAuthor = true,
  showDate = true,
  showExcerpt = true,
  showTags = false,
  showViews = false,
  showReadTime = true,
  showInteractions = false,
  onPostClick,
  className,
  emptyMessage = 'Nenhum post encontrado',
  emptyIcon = '📝',
}) => {
  const config = layoutConfig[layout];
  const columnClass = config.columns[columns];

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
        <span className="ml-3 text-cyber-muted">Carregando posts...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-cyber-text mb-2">
          Erro ao carregar posts
        </h3>
        <p className="text-cyber-muted">{error}</p>
      </div>
    );
  }

  // Empty state
  if (!posts || posts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">{emptyIcon}</div>
        <h3 className="text-lg font-semibold text-cyber-text mb-2">
          {emptyMessage}
        </h3>
        <p className="text-cyber-muted">
          Tente ajustar os filtros ou volte mais tarde.
        </p>
      </div>
    );
  }

  // Render especial para layout featured (primeiro post em destaque)
  if (layout === 'featured' && posts.length > 0) {
    const [featuredPost, ...regularPosts] = posts;
    
    return (
      <div className={twMerge(clsx('space-y-8', className))}>
        {/* Post em destaque */}
        <PostCard
          post={featuredPost}
          variant="hero"
          showAuthor={showAuthor}
          showDate={showDate}
          showExcerpt={showExcerpt}
          showTags={showTags}
          showViews={showViews}
          showReadTime={showReadTime}
          showInteractions={showInteractions}
          onClick={onPostClick}
        />

        {/* Posts regulares */}
        {regularPosts.length > 0 && (
          <div className={clsx(config.container, columnClass)}>
            {regularPosts.map((post) => (
              <PostCard
                key={post.id}
                post={post}
                variant={config.cardVariant}
                showAuthor={showAuthor}
                showDate={showDate}
                showExcerpt={showExcerpt}
                showTags={showTags}
                showViews={showViews}
                showReadTime={showReadTime}
                showInteractions={showInteractions}
                onClick={onPostClick}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  // Render para layouts masonry (precisa de tratamento especial)
  if (layout === 'masonry') {
    return (
      <div className={twMerge(clsx(config.container, columnClass, className))}>
        {posts.map((post) => (
          <div key={post.id} className="break-inside-avoid mb-6">
            <PostCard
              post={post}
              variant={config.cardVariant}
              showAuthor={showAuthor}
              showDate={showDate}
              showExcerpt={showExcerpt}
              showTags={showTags}
              showViews={showViews}
              showReadTime={showReadTime}
              showInteractions={showInteractions}
              onClick={onPostClick}
            />
          </div>
        ))}
      </div>
    );
  }

  // Render padrão para grid e list
  return (
    <div className={twMerge(clsx(config.container, columnClass, className))}>
      {posts.map((post) => (
        <PostCard
          key={post.id}
          post={post}
          variant={config.cardVariant}
          showAuthor={showAuthor}
          showDate={showDate}
          showExcerpt={showExcerpt}
          showTags={showTags}
          showViews={showViews}
          showReadTime={showReadTime}
          showInteractions={showInteractions}
          onClick={onPostClick}
        />
      ))}
    </div>
  );
};

// ============================================================================
// COMPONENTE DE LOADING SKELETON
// ============================================================================

export const PostGridSkeleton: React.FC<{
  layout?: 'grid' | 'list';
  columns?: 1 | 2 | 3 | 4;
  count?: number;
}> = ({ layout = 'grid', columns = 3, count = 6 }) => {
  const config = layoutConfig[layout];
  const columnClass = config.columns[columns];

  const SkeletonCard = () => (
    <div className="animate-pulse">
      <div className="aspect-video bg-cyber-surface rounded-lg mb-4" />
      <div className="space-y-3">
        <div className="h-4 bg-cyber-surface rounded w-3/4" />
        <div className="h-3 bg-cyber-surface rounded w-full" />
        <div className="h-3 bg-cyber-surface rounded w-2/3" />
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-cyber-surface rounded-full" />
          <div className="h-3 bg-cyber-surface rounded w-20" />
        </div>
      </div>
    </div>
  );

  return (
    <div className={clsx(config.container, columnClass)}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} />
      ))}
    </div>
  );
};

export default PostGrid;
