/**
 * @fileoverview Sistema de Tipografia Mobile Otimizada
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

/* ============================================================================
   TIPOGRAFIA MOBILE - SISTEMA RESPONSIVO
   ============================================================================ */

/* Base Typography - Mobile First */
:root {
  /* Mobile Typography Scale (320px-768px) */
  --text-xs-mobile: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);     /* 12px-14px */
  --text-sm-mobile: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);       /* 14px-16px */
  --text-base-mobile: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);       /* 16px-18px */
  --text-lg-mobile: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);      /* 18px-20px */
  --text-xl-mobile: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);       /* 20px-24px */
  --text-2xl-mobile: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);        /* 24px-30px */
  --text-3xl-mobile: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);   /* 30px-36px */
  --text-4xl-mobile: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);        /* 36px-48px */

  /* Line Heights - Otimizados para leitura mobile */
  --leading-tight-mobile: 1.2;
  --leading-snug-mobile: 1.3;
  --leading-normal-mobile: 1.4;
  --leading-relaxed-mobile: 1.5;
  --leading-loose-mobile: 1.6;

  /* Letter Spacing - Melhor legibilidade em telas pequenas */
  --tracking-tighter-mobile: -0.02em;
  --tracking-tight-mobile: -0.01em;
  --tracking-normal-mobile: 0em;
  --tracking-wide-mobile: 0.01em;
  --tracking-wider-mobile: 0.02em;
}

/* ============================================================================
   CLASSES UTILITÁRIAS MOBILE
   ============================================================================ */

/* Text Sizes - Mobile Optimized */
.text-xs-mobile {
  font-size: var(--text-xs-mobile);
  line-height: var(--leading-normal-mobile);
  letter-spacing: var(--tracking-normal-mobile);
}

.text-sm-mobile {
  font-size: var(--text-sm-mobile);
  line-height: var(--leading-normal-mobile);
  letter-spacing: var(--tracking-normal-mobile);
}

.text-base-mobile {
  font-size: var(--text-base-mobile);
  line-height: var(--leading-normal-mobile);
  letter-spacing: var(--tracking-normal-mobile);
}

.text-lg-mobile {
  font-size: var(--text-lg-mobile);
  line-height: var(--leading-snug-mobile);
  letter-spacing: var(--tracking-tight-mobile);
}

.text-xl-mobile {
  font-size: var(--text-xl-mobile);
  line-height: var(--leading-snug-mobile);
  letter-spacing: var(--tracking-tight-mobile);
}

.text-2xl-mobile {
  font-size: var(--text-2xl-mobile);
  line-height: var(--leading-tight-mobile);
  letter-spacing: var(--tracking-tighter-mobile);
}

.text-3xl-mobile {
  font-size: var(--text-3xl-mobile);
  line-height: var(--leading-tight-mobile);
  letter-spacing: var(--tracking-tighter-mobile);
}

.text-4xl-mobile {
  font-size: var(--text-4xl-mobile);
  line-height: var(--leading-tight-mobile);
  letter-spacing: var(--tracking-tighter-mobile);
}

/* ============================================================================
   COMPONENTES ESPECÍFICOS MOBILE
   ============================================================================ */

/* Títulos de Cards Mobile */
.card-title-mobile {
  font-size: var(--text-lg-mobile);
  line-height: var(--leading-snug-mobile);
  letter-spacing: var(--tracking-tight-mobile);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Texto de Cards Mobile */
.card-text-mobile {
  font-size: var(--text-sm-mobile);
  line-height: var(--leading-relaxed-mobile);
  letter-spacing: var(--tracking-normal-mobile);
  color: var(--cyber-muted);
}

/* Meta informações Mobile */
.meta-text-mobile {
  font-size: var(--text-xs-mobile);
  line-height: var(--leading-normal-mobile);
  letter-spacing: var(--tracking-wide-mobile);
  font-weight: 500;
  text-transform: uppercase;
}

/* Botões Mobile */
.button-text-mobile {
  font-size: var(--text-sm-mobile);
  line-height: var(--leading-tight-mobile);
  letter-spacing: var(--tracking-wide-mobile);
  font-weight: 600;
}

/* Hero Title Mobile */
.hero-title-mobile {
  font-size: var(--text-3xl-mobile);
  line-height: var(--leading-tight-mobile);
  letter-spacing: var(--tracking-tighter-mobile);
  font-weight: 800;
  margin-bottom: 1rem;
}

/* Hero Subtitle Mobile */
.hero-subtitle-mobile {
  font-size: var(--text-base-mobile);
  line-height: var(--leading-relaxed-mobile);
  letter-spacing: var(--tracking-normal-mobile);
  font-weight: 400;
  opacity: 0.9;
}

/* ============================================================================
   BREAKPOINT ESPECÍFICOS
   ============================================================================ */

/* Extra Small Mobile (320px-480px) */
@media (max-width: 480px) {
  .text-hero-xs {
    font-size: clamp(1.5rem, 4vw, 2rem);
    line-height: 1.2;
    letter-spacing: -0.02em;
  }
  
  .text-card-xs {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    line-height: 1.4;
  }
  
  .text-meta-xs {
    font-size: clamp(0.75rem, 2vw, 0.875rem);
    line-height: 1.3;
  }
}

/* Small Mobile (480px-640px) */
@media (min-width: 480px) and (max-width: 640px) {
  .text-hero-sm {
    font-size: clamp(2rem, 5vw, 2.5rem);
    line-height: 1.2;
    letter-spacing: -0.015em;
  }
  
  .text-card-sm {
    font-size: clamp(1rem, 3vw, 1.125rem);
    line-height: 1.4;
  }
}

/* Large Mobile/Small Tablet (640px-768px) */
@media (min-width: 640px) and (max-width: 768px) {
  .text-hero-md {
    font-size: clamp(2.5rem, 6vw, 3rem);
    line-height: 1.1;
    letter-spacing: -0.01em;
  }
}

/* ============================================================================
   UTILITÁRIOS DE TRUNCAMENTO MOBILE
   ============================================================================ */

/* Truncamento inteligente para mobile */
.truncate-mobile-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.truncate-mobile-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.truncate-mobile-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ============================================================================
   ACESSIBILIDADE MOBILE
   ============================================================================ */

/* Melhor contraste para leitura mobile */
@media (max-width: 768px) {
  .text-cyber-muted {
    color: #a0a0a0; /* Mais claro que o padrão para melhor legibilidade */
  }
  
  .text-cyber-text {
    color: #ffffff; /* Contraste máximo */
  }
}

/* Reduzir motion para usuários sensíveis */
@media (prefers-reduced-motion: reduce) {
  .text-animate-mobile {
    animation: none !important;
    transition: none !important;
  }
}

/* ============================================================================
   PERFORMANCE MOBILE
   ============================================================================ */

/* Otimizações de rendering para mobile */
.text-optimized-mobile {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-display: swap;
}

/* Prevenção de zoom em inputs mobile */
.input-no-zoom-mobile {
  font-size: 16px !important; /* Previne zoom no iOS */
  transform-origin: left top;
}

/* ============================================================================
   UTILITÁRIOS MOBILE
   ============================================================================ */

/* Scrollbar hide para mobile */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* ============================================================================
   PERFORMANCE MOBILE OPTIMIZATIONS
   ============================================================================ */

/* Reduzir animações em mobile para melhor performance */
@media (max-width: 768px) {
  * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
  }

  /* Desabilitar animações complexas em mobile */
  .animate-pulse,
  .animate-spin,
  .animate-bounce {
    animation-duration: 0.5s;
  }

  /* Otimizar transforms para GPU */
  .transform {
    will-change: transform;
    transform: translateZ(0);
  }

  /* Reduzir blur effects em mobile */
  .backdrop-blur-md {
    backdrop-filter: blur(4px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(2px);
  }
}

/* Otimizações para dispositivos de baixa performance */
@media (max-width: 480px) {
  /* Desabilitar shadows complexos */
  .shadow-2xl,
  .shadow-xl {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Simplificar gradients */
  .bg-gradient-to-r,
  .bg-gradient-to-br {
    background: var(--cyber-surface);
  }
}

/* ============================================================================
   DARK MODE MOBILE
   ============================================================================ */

@media (prefers-color-scheme: dark) {
  :root {
    --text-contrast-mobile: 1.2; /* Aumenta contraste no dark mode */
  }

  .text-cyber-text {
    filter: contrast(var(--text-contrast-mobile));
  }
}
