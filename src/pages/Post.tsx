/**
 * @fileoverview Página de Post individual do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import { FaArrowRight, FaGithub, FaLinkedin, FaTwitter } from 'react-icons/fa';
import { Link, useNavigate, useParams } from 'react-router';
import MarkdownRenderer from '../components/markdown/MarkdownRenderer';
import { JsonLdSchema, SEOHead } from '../components/seo';
import { Breadcrumbs, Button, Card, CardContent, ResponsiveImage } from '../components/ui';
import AuthorAvatar from '../components/ui/AuthorAvatar';
import { usePosts } from '../hooks/usePosts';
import { useMobileDetection } from '../hooks/useMobileDetection';
import useSEO from '../hooks/useSEO';
import type { Post } from '../types';
import { generateBreadcrumbs } from '../utils/seoUtils';
// ============================================================================
// INTERFACES
// ============================================================================

interface PostPageProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de post individual com conteúdo completo
 */
export const PostPage: React.FC<PostPageProps> = ({ className }) => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { isMobile } = useMobileDetection();
  const [post, setPost] = useState<Post | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);

  // Hook para buscar posts do Supabase
  const { posts, getPostBySlug } = usePosts();

  // SEO dinâmico para o post
  const seoData = useSEO({
    post: post || undefined,
    pageType: 'post',
  });

  // Breadcrumbs para o post
  const breadcrumbs = post
    ? generateBreadcrumbs(`/post/${post.slug}`, post, post.category.name)
    : [];

  // Função para navegar para o post
  const handlePostClick = (post: Post) => {
    navigate(`/post/${post.slug}`);
  };

  // Carrega o post e posts relacionados
  useEffect(() => {
    const loadPost = async () => {
      if (!slug) return;

      try {
        setLoading(true);

        // Busca o post pelo slug usando o hook
        const { data: foundPost, error } = await getPostBySlug(slug);

        if (error) {
          console.error('Erro ao carregar post:', error);
          setPost(null);
          return;
        }

        // Tags já são carregadas corretamente pelo getPostBySlug() usando estrutura normalizada

        setPost(foundPost);

        // 🔍 DEBUG: Log das tags do post
        console.log('🏷️ [Post] Tags do post:', {
          postTitle: foundPost?.title,
          tags: foundPost?.tags,
          tagsType: typeof foundPost?.tags,
          tagsLength: foundPost?.tags?.length,
          firstTag: foundPost?.tags?.[0],
        });

        // Posts relacionados (mesma categoria, exceto o atual)
        if (foundPost && posts.length > 0) {
          const related = posts
            .filter(
              (p) =>
                p.category.id === foundPost.category.id && p.id !== foundPost.id
            )
            .slice(0, 3);
          setRelatedPosts(related);
        }
      } catch (error) {
        console.error('Erro ao carregar post:', error);
        setPost(null);
      } finally {
        setLoading(false);
      }
    };

    loadPost();
  }, [slug, getPostBySlug, posts]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando post...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-cyber-text mb-4">404</h1>
          <p className="text-cyber-muted mb-6">Post não encontrado</p>
          <Button>Voltar ao Início</Button>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema type="post" post={post} breadcrumbs={breadcrumbs} />

      <div className={clsx('max-w-4xl mx-auto', className)}>
        {/* Breadcrumb - Mobile Optimized */}
        <Breadcrumbs
          items={breadcrumbs}
          className={clsx(
            'mb-6 mobile-xs:mb-4 sm:mb-8',
            // Mobile: scroll horizontal se necessário
            'overflow-x-auto scrollbar-hide'
          )}
        />

        {/* Post Header - Mobile Optimized */}
        <header className="mb-6 mobile-xs:mb-4 sm:mb-8">
          <div className="mb-3 mobile-xs:mb-2 sm:mb-4">
            <span
              className={clsx(
                'inline-block px-3 py-1 rounded-full font-medium',
                'text-xs-mobile sm:text-sm'
              )}
              style={{
                backgroundColor: `${post.category.color}20`,
                color: post.category.color,
                border: `1px solid ${post.category.color}30`,
              }}>
              {post.category.name}
            </span>
          </div>

          <h1 className="text-3xl-mobile sm:text-4xl md:text-5xl font-bold text-cyber-text mb-4 mobile-xs:mb-3 sm:mb-6 leading-tight">
            {post.title}
          </h1>

          <p className="text-base-mobile sm:text-xl text-cyber-muted mb-6 mobile-xs:mb-4 sm:mb-8 leading-relaxed">
            {post.excerpt}
          </p>

          {/* Meta Info - Mobile Optimized */}
          <div className={clsx(
            'flex flex-col gap-4 bg-cyber-surface rounded-lg border border-cyber-border',
            'p-4 mobile-xs:p-3 sm:p-6',
            'sm:flex-row sm:items-center sm:justify-between'
          )}>
            <div className="flex items-center space-x-3 mobile-xs:space-x-2 sm:space-x-4">
              <AuthorAvatar
                avatar={post.author.avatar}
                name={post.author.name}
                size={isMobile ? "md" : "lg"}
                className={isMobile ? "w-10 h-10" : "w-12 h-12"}
              />
              <div className="min-w-0 flex-1">
                <p className="font-medium text-cyber-text text-sm-mobile sm:text-base">
                  {post.author.name}
                </p>
                <p className={clsx(
                  'text-cyber-muted truncate-mobile-1 sm:line-clamp-none',
                  'text-xs-mobile sm:text-sm'
                )}>
                  {post.author.bio}
                </p>
              </div>
            </div>

            {/* Stats - Mobile: Grid 2x2, Desktop: Horizontal */}
            <div className={clsx(
              'text-cyber-muted',
              'text-xs-mobile sm:text-sm',
              // Mobile: Grid 2x2
              'grid grid-cols-2 gap-2 mobile-xs:gap-1',
              // Desktop: Horizontal
              'sm:flex sm:items-center sm:space-x-6 sm:gap-0'
            )}>
              <div className="flex items-center space-x-1">
                <span>📅</span>
                <span className="truncate">
                  {new Date(post.date).toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit',
                    year: isMobile ? '2-digit' : 'numeric'
                  })}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <span>⏱️</span>
                <span>{post.readTime} min</span>
              </div>
              {!isMobile && (
                <>
                  <div className="flex items-center space-x-1">
                    <span>👁️</span>
                    <span>{post.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>❤️</span>
                    <span>{post.likes}</span>
                  </div>
                </>
              )}
              {/* Mobile: Views e Likes na segunda linha */}
              {isMobile && (
                <>
                  <div className="flex items-center space-x-1">
                    <span>👁️</span>
                    <span>{post.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span>❤️</span>
                    <span>{post.likes}</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </header>

        {/* Featured Image - Mobile Optimized */}
        <div className="mb-6 mobile-xs:mb-4 sm:mb-8">
          <ResponsiveImage
            src={post.thumbnail}
            alt={post.title}
            aspectRatio="wide"
            className={clsx(
              'w-full rounded-lg',
              'h-48 mobile-xs:h-40 sm:h-64 md:h-96'
            )}
            sizes={{
              mobile: '320px, 480px, 640px',
              tablet: '768px, 1024px',
              desktop: '1280px, 1920px'
            }}
            breakpoints="(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw"
            lazy={false} // Above fold content
            placeholder="blur"
            imgProps={{
              className: "w-full h-full object-cover"
            }}
          />
        </div>

        {/* Post Content - Mobile Optimized */}
        <Card className="mb-8 mobile-xs:mb-6 sm:mb-12 shadow-lg shadow-cyber-bg/20">
          <CardContent className={clsx(
            'p-4 mobile-xs:p-3 sm:p-6 lg:p-8'
          )}>
            <article className={clsx(
              'prose prose-invert max-w-none',
              // Mobile: prose-sm, Desktop: prose-lg
              'prose-sm mobile-xs:prose-xs sm:prose-base lg:prose-lg',
              // Mobile typography adjustments
              'mobile-xs:[&>h1]:text-xl-mobile',
              'mobile-xs:[&>h2]:text-lg-mobile',
              'mobile-xs:[&>h3]:text-base-mobile',
              'mobile-xs:[&>p]:text-sm-mobile',
              'mobile-xs:[&>li]:text-sm-mobile',
              // Code blocks mobile
              'mobile-xs:[&>pre]:text-xs',
              'mobile-xs:[&>pre]:overflow-x-auto',
              'mobile-xs:[&>code]:text-xs'
            )}>
              <MarkdownRenderer
                content={post.content}
                className="text-cyber-text leading-relaxed"
                isLegacyContent={true}
              />
            </article>
          </CardContent>
        </Card>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <Card className="mb-8 shadow-md shadow-cyber-bg/10">
            <CardContent className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-cyber-text mb-4">
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => {
                  // Verifica se tag é objeto ou string
                  const tagData =
                    typeof tag === 'string'
                      ? {
                          id: tag,
                          name: tag,
                          slug: (tag as string)
                            .toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
                            .replace(/\s+/g, '-') // Substitui espaços por hífens
                            .replace(/-+/g, '-') // Remove hífens duplicados
                            .trim(),
                        }
                      : tag;

                  return (
                    <Link
                      key={tagData.id || index}
                      to={`/tags/${tagData.slug}`}
                      className="px-3 py-1 rounded-full text-sm border border-cyber-border text-cyber-muted hover:text-neon-cyan hover:border-neon-cyan transition-colors">
                      #{tagData.name}
                    </Link>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Social Share */}
        <Card className="mb-12 shadow-md shadow-cyber-bg/10">
          <CardContent className="p-4 sm:p-6">
            <h3 className="text-lg font-semibold text-cyber-text mb-4">
              Compartilhar
            </h3>
            <div className="flex space-x-4">
              <Button size="sm" variant="secondary">
                <FaTwitter />
              </Button>
              <Button size="sm" variant="secondary">
                <FaLinkedin />
              </Button>
              <Button size="sm" variant="secondary">
                <FaGithub />
              </Button>
              <Button size="sm" variant="secondary">
                🔗 Copiar Link
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Comments Section (Placeholder) */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-cyber-text mb-6">
            💬 Comentários
          </h2>

          <Card>
            <CardContent>
              <div className="text-center py-12">
                <p className="text-cyber-muted mb-4">
                  Sistema de comentários será implementado em breve
                </p>
                <Button variant="secondary" size="sm">
                  Notificar quando disponível
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Related Posts - Mobile Optimized */}
        {relatedPosts.length > 0 && (
          <section className="mb-8 mobile-xs:mb-6 sm:mb-12">
            <h2 className="text-xl-mobile sm:text-2xl font-bold text-cyber-text mb-4 mobile-xs:mb-3 sm:mb-6">
              📚 Posts Relacionados
            </h2>

            <div className={clsx(
              'grid gap-4 mobile-xs:gap-3 sm:gap-6',
              // Mobile: 1 coluna, Tablet: 2 colunas, Desktop: 3 colunas
              'grid-cols-1 tablet:grid-cols-2 lg:grid-cols-3'
            )}>
              {relatedPosts.map((relatedPost) => (
                <div
                  key={relatedPost.id}
                  className={clsx(
                    'relative rounded-lg overflow-hidden cursor-pointer group',
                    // Mobile: altura menor
                    'h-48 mobile-xs:h-40 sm:h-64 lg:h-72'
                  )}
                  onClick={() => handlePostClick(relatedPost)}>
                  {/* Background Image */}
                  <ResponsiveImage
                    src={relatedPost.thumbnail}
                    alt={relatedPost.title}
                    aspectRatio="video"
                    className="absolute inset-0"
                    sizes={{
                      mobile: '320px, 480px',
                      tablet: '300px, 400px',
                      desktop: '300px, 400px'
                    }}
                    breakpoints={
                      isMobile ? '(max-width: 640px) 100vw, 100vw' :
                      '(max-width: 768px) 50vw, 33vw'
                    }
                    lazy={true}
                    placeholder="blur"
                    imgProps={{
                      className: "w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    }}
                  />
                  {/* Blur overlay para melhor legibilidade */}
                  <div className="absolute inset-0 bg-cyber-bg/20 backdrop-blur-[0.5px]" />

                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-cyber-bg/90 via-cyber-bg/20 to-transparent" />

                  {/* Category Badge */}
                  <div className="absolute top-2 left-2 sm:top-4 sm:left-4 z-10">
                    <span
                      className="px-2 py-1 rounded-full text-xs font-bold backdrop-blur-sm border"
                      style={{
                        backgroundColor: `${relatedPost.category.color}20`,
                        color: relatedPost.category.color,
                        borderColor: `${relatedPost.category.color}40`,
                      }}>
                      {relatedPost.category.name}
                    </span>
                  </div>

                  {/* Content Overlay - Mobile Optimized */}
                  <div className="absolute bottom-0 left-0 right-0 p-2 mobile-xs:p-1.5 sm:p-3 lg:p-4 z-10">
                    <h3 className={clsx(
                      'font-bold text-white mb-1.5 mobile-xs:mb-1 sm:mb-2',
                      'group-hover:text-neon-cyan transition-colors line-clamp-2',
                      'text-sm-mobile sm:text-base lg:text-lg'
                    )}>
                      {relatedPost.title}
                    </h3>

                    {!isMobile && (
                      <p className="text-gray-200 text-sm mb-3 line-clamp-2 opacity-90">
                        {relatedPost.excerpt}
                      </p>
                    )}

                    {/* Meta Info - Mobile Optimized */}
                    <div className={clsx(
                      'flex items-center text-gray-300 mb-2 mobile-xs:mb-1.5 sm:mb-3',
                      'text-xs-mobile sm:text-xs',
                      // Mobile: apenas data e tempo, Desktop: inclui views
                      isMobile ? 'justify-start space-x-3' : 'justify-between'
                    )}>
                      <div className="flex items-center space-x-2 mobile-xs:space-x-1.5 sm:space-x-3">
                        <span className="flex items-center space-x-1">
                          <span>📅</span>
                          <span>
                            {new Date(relatedPost.date).toLocaleDateString('pt-BR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: isMobile ? '2-digit' : 'numeric'
                            })}
                          </span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <span>⏱️</span>
                          <span>{relatedPost.readTime} min</span>
                        </span>
                      </div>
                      {!isMobile && (
                        <span className="flex items-center space-x-1">
                          <span>👁️</span>
                          <span>{relatedPost.views}</span>
                        </span>
                      )}
                    </div>

                    {/* CTA Button - Mobile Optimized */}
                    <div className="pt-0.5 mobile-xs:pt-0 sm:pt-1">
                      <Button
                        variant="primary"
                        size={isMobile ? "xs" : "sm"}
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePostClick(relatedPost);
                        }}
                        className={clsx(
                          'group/btn',
                          'text-xs-mobile sm:text-xs',
                          'button-text-mobile'
                        )}>
                        <span>{isMobile ? "Ler" : "Leia mais"}</span>
                        <FaArrowRight className={clsx(
                          'ml-1 group-hover/btn:translate-x-1 transition-transform duration-300',
                          'text-xs-mobile sm:text-xs'
                        )} />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Navigation - Mobile Optimized */}
        <nav className={clsx(
          'flex items-center gap-2 mobile-xs:gap-1.5 sm:gap-4',
          // Mobile: stack vertical se necessário
          'flex-col mobile-lg:flex-row',
          // Desktop: justify between
          'sm:justify-between'
        )}>
          <Button
            variant="secondary"
            size={isMobile ? "sm" : "md"}
            leftIcon="←"
            className="w-full mobile-lg:w-auto button-text-mobile">
            {isMobile ? "Anterior" : "Post Anterior"}
          </Button>
          <Button
            variant="secondary"
            size={isMobile ? "sm" : "md"}
            className="w-full mobile-lg:w-auto button-text-mobile">
            {isMobile ? "Blog" : "Voltar ao Blog"}
          </Button>
          <Button
            variant="secondary"
            size={isMobile ? "sm" : "md"}
            rightIcon="→"
            className="w-full mobile-lg:w-auto button-text-mobile">
            {isMobile ? "Próximo" : "Próximo Post"}
          </Button>
        </nav>
      </div>
    </>
  );
};

export default PostPage;
