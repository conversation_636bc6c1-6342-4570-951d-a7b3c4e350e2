/**
 * @fileoverview Página de Documentação do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Book, Code, FileText, Search } from 'lucide-react';
import { useState } from 'react';
import { Card, CardContent, CardHeader, Input } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface DocsPageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de documentação técnica
 */
export const Docs: React.FC<DocsPageProps> = ({ className }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const docsClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  const docSections = [
    {
      title: 'Introdução',
      icon: <Book className="w-5 h-5" />,
      items: ['Visão Geral', 'Instalação', 'Configuração Inicial']
    },
    {
      title: 'API Reference',
      icon: <Code className="w-5 h-5" />,
      items: ['Endpoints', 'Autenticação', 'Rate Limiting']
    },
    {
      title: 'Guias',
      icon: <FileText className="w-5 h-5" />,
      items: ['Como Contribuir', 'Deployment', 'Troubleshooting']
    }
  ];

  return (
    <div className={docsClasses}>
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Book className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Documentação
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Guias completos e referências técnicas do Blueprint Blog v2
          </p>
        </div>

        {/* Navegação Rápida */}
        <Card className="mb-8">
          <CardHeader title="🚀 Navegação Rápida" />
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <a
                href="#quick-start"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-neon-cyan/20 flex items-center justify-center text-neon-cyan group-hover:bg-neon-cyan/30 transition-colors">
                    <Code className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyber-text">Quick Start</h3>
                    <p className="text-cyber-muted text-sm">Comece rapidamente</p>
                  </div>
                </div>
              </a>

              <a
                href="#api-overview"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-neon-purple/20 flex items-center justify-center text-neon-purple group-hover:bg-neon-purple/30 transition-colors">
                    <FileText className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyber-text">API Reference</h3>
                    <p className="text-cyber-muted text-sm">Documentação da API</p>
                  </div>
                </div>
              </a>

              <a
                href="#contributing"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 rounded-full bg-neon-cyan/20 flex items-center justify-center text-neon-cyan group-hover:bg-neon-cyan/30 transition-colors">
                    <Book className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyber-text">Contribuir</h3>
                    <p className="text-cyber-muted text-sm">Como contribuir</p>
                  </div>
                </div>
              </a>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader title="📚 Navegação" />
              <CardContent>
                {/* Search */}
                <div className="mb-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cyber-muted" />
                    <Input
                      type="text"
                      placeholder="Buscar na documentação..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Navigation */}
                <div className="space-y-4">
                  {docSections.map((section, index) => (
                    <div key={index}>
                      <div className="flex items-center space-x-2 text-cyber-text font-medium mb-2">
                        {section.icon}
                        <span>{section.title}</span>
                      </div>
                      <ul className="space-y-1 ml-7">
                        {section.items.map((item, itemIndex) => (
                          <li key={itemIndex}>
                            <a
                              href={`#${item.toLowerCase().replace(/\s+/g, '-')}`}
                              className="text-cyber-muted hover:text-neon-cyan transition-colors text-sm"
                            >
                              {item}
                            </a>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Quick Start */}
            <Card>
              <CardHeader title="🚀 Quick Start" />
              <CardContent>
                <div className="space-y-6">
                  <p className="text-cyber-muted">
                    Comece rapidamente com o Blueprint Blog v2:
                  </p>

                  {/* Pré-requisitos */}
                  <div>
                    <h3 className="text-lg font-semibold text-cyber-text mb-3">📋 Pré-requisitos</h3>
                    <ul className="list-disc list-inside space-y-1 text-cyber-muted">
                      <li>Node.js 18+ e npm/yarn</li>
                      <li>Conta no Supabase</li>
                      <li>Git instalado</li>
                    </ul>
                  </div>

                  {/* Instalação */}
                  <div>
                    <h3 className="text-lg font-semibold text-cyber-text mb-3">⚡ Instalação</h3>
                    <div className="bg-cyber-surface rounded-lg p-4 border border-cyber-border">
                      <pre className="text-sm text-neon-cyan overflow-x-auto">
                        <code>{`# Clone o repositório
git clone https://github.com/blueprintblog/v2.git
cd blueprint-blog-v2

# Instale as dependências
npm install

# Configure as variáveis de ambiente
cp .env.example .env.local

# Configure o Supabase
# Edite .env.local com suas credenciais

# Execute em modo de desenvolvimento
npm run dev

# Acesse http://localhost:5173`}</code>
                      </pre>
                    </div>
                  </div>

                  {/* Configuração */}
                  <div>
                    <h3 className="text-lg font-semibold text-cyber-text mb-3">⚙️ Configuração</h3>
                    <div className="bg-cyber-surface rounded-lg p-4 border border-cyber-border">
                      <pre className="text-sm text-neon-cyan overflow-x-auto">
                        <code>{`# .env.local
VITE_SUPABASE_URL=sua_url_do_supabase
VITE_SUPABASE_ANON_KEY=sua_chave_anonima
VITE_SITE_URL=http://localhost:5173`}</code>
                      </pre>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* API Overview */}
            <Card>
              <CardHeader title="🔌 API Overview" />
              <CardContent>
                <div className="space-y-6">
                  <p className="text-cyber-muted">
                    O Blueprint Blog v2 oferece uma API RESTful completa baseada no Supabase:
                  </p>

                  {/* Base URL */}
                  <div>
                    <h3 className="text-lg font-semibold text-cyber-text mb-3">🌐 Base URL</h3>
                    <div className="bg-cyber-surface rounded-lg p-3 border border-cyber-border">
                      <code className="text-neon-cyan">https://sua-url.supabase.co/rest/v1/</code>
                    </div>
                  </div>

                  {/* Endpoints */}
                  <div>
                    <h3 className="text-lg font-semibold text-cyber-text mb-3">📋 Principais Endpoints</h3>
                    <div className="grid grid-cols-1 gap-3">
                      {[
                        { method: 'GET', endpoint: '/posts', desc: 'Listar posts publicados', auth: false },
                        { method: 'GET', endpoint: '/posts?id=eq.{id}', desc: 'Obter post específico', auth: false },
                        { method: 'POST', endpoint: '/posts', desc: 'Criar novo post', auth: true },
                        { method: 'PATCH', endpoint: '/posts?id=eq.{id}', desc: 'Atualizar post', auth: true },
                        { method: 'GET', endpoint: '/categories', desc: 'Listar categorias', auth: false },
                        { method: 'GET', endpoint: '/tags', desc: 'Listar tags', auth: false },
                        { method: 'GET', endpoint: '/profiles', desc: 'Listar perfis públicos', auth: false }
                      ].map((api, index) => (
                        <div key={index} className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className={clsx(
                                'px-2 py-1 rounded text-xs font-mono',
                                api.method === 'GET' ? 'bg-green-500/20 text-green-400' :
                                api.method === 'POST' ? 'bg-blue-500/20 text-blue-400' :
                                'bg-yellow-500/20 text-yellow-400'
                              )}>
                                {api.method}
                              </span>
                              <code className="text-neon-cyan text-sm">{api.endpoint}</code>
                            </div>
                            {api.auth && (
                              <span className="px-2 py-1 rounded text-xs bg-red-500/20 text-red-400">
                                🔒 Auth
                              </span>
                            )}
                          </div>
                          <p className="text-cyber-muted text-sm">{api.desc}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Autenticação */}
                  <div>
                    <h3 className="text-lg font-semibold text-cyber-text mb-3">🔐 Autenticação</h3>
                    <div className="bg-cyber-surface rounded-lg p-4 border border-cyber-border">
                      <pre className="text-sm text-neon-cyan overflow-x-auto">
                        <code>{`# Header de autenticação
Authorization: Bearer YOUR_JWT_TOKEN

# Exemplo com curl
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
     -H "apikey: YOUR_ANON_KEY" \\
     https://sua-url.supabase.co/rest/v1/posts`}</code>
                      </pre>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contributing */}
            <Card>
              <CardHeader title="🤝 Contribuindo" />
              <CardContent>
                <div className="space-y-4">
                  <p className="text-cyber-muted">
                    Adoraríamos sua contribuição! Aqui está como começar:
                  </p>
                  
                  <ol className="list-decimal list-inside space-y-2 text-cyber-muted">
                    <li>Faça um fork do repositório</li>
                    <li>Crie uma branch para sua feature</li>
                    <li>Faça suas alterações</li>
                    <li>Adicione testes se necessário</li>
                    <li>Envie um pull request</li>
                  </ol>

                  <div className="flex space-x-4 mt-6">
                    <a
                      href="https://github.com/blueprintblog/v2"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-neon-cyan/20 text-neon-cyan rounded-lg hover:bg-neon-cyan/30 transition-colors"
                    >
                      <Code className="w-4 h-4 mr-2" />
                      Ver no GitHub
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Docs;
