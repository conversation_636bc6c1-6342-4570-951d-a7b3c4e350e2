/**
 * @fileoverview Página de Documentação do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Book, Code, FileText, Search } from 'lucide-react';
import { useState } from 'react';
import { Card, CardContent, CardHeader, Input } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface DocsPageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de documentação técnica
 */
export const Docs: React.FC<DocsPageProps> = ({ className }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const docsClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  const docSections = [
    {
      title: 'Introdução',
      icon: <Book className="w-5 h-5" />,
      items: ['Visão Geral', 'Instalação', 'Configuração Inicial']
    },
    {
      title: 'API Reference',
      icon: <Code className="w-5 h-5" />,
      items: ['Endpoints', 'Autenticação', 'Rate Limiting']
    },
    {
      title: 'Guias',
      icon: <FileText className="w-5 h-5" />,
      items: ['Como Contribuir', 'Deployment', 'Troubleshooting']
    }
  ];

  return (
    <div className={docsClasses}>
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Book className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Documentação
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Guias completos e referências técnicas do Blueprint Blog v2
          </p>
        </div>

        {/* Status de Desenvolvimento */}
        <Card className="mb-8 border-amber-500/30 bg-amber-500/5">
          <CardHeader 
            title="🚧 Em Desenvolvimento" 
            subtitle="Esta página está sendo construída"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="text-cyber-muted">
                <p className="mb-2">Documentação planejada:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Guia de instalação e configuração</li>
                  <li>Documentação completa da API</li>
                  <li>Tutoriais de desenvolvimento</li>
                  <li>Exemplos de código e snippets</li>
                  <li>Guias de contribuição</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader title="📚 Navegação" />
              <CardContent>
                {/* Search */}
                <div className="mb-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cyber-muted" />
                    <Input
                      type="text"
                      placeholder="Buscar na documentação..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Navigation */}
                <div className="space-y-4">
                  {docSections.map((section, index) => (
                    <div key={index}>
                      <div className="flex items-center space-x-2 text-cyber-text font-medium mb-2">
                        {section.icon}
                        <span>{section.title}</span>
                      </div>
                      <ul className="space-y-1 ml-7">
                        {section.items.map((item, itemIndex) => (
                          <li key={itemIndex}>
                            <a
                              href={`#${item.toLowerCase().replace(/\s+/g, '-')}`}
                              className="text-cyber-muted hover:text-neon-cyan transition-colors text-sm"
                            >
                              {item}
                            </a>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Quick Start */}
            <Card>
              <CardHeader title="🚀 Quick Start" />
              <CardContent>
                <div className="space-y-4">
                  <p className="text-cyber-muted">
                    Comece rapidamente com o Blueprint Blog v2:
                  </p>
                  
                  <div className="bg-cyber-surface rounded-lg p-4 border border-cyber-border">
                    <pre className="text-sm text-neon-cyan">
                      <code>{`# Clone o repositório
git clone https://github.com/blueprintblog/v2.git

# Instale as dependências
npm install

# Configure as variáveis de ambiente
cp .env.example .env.local

# Execute em modo de desenvolvimento
npm run dev`}</code>
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* API Overview */}
            <Card>
              <CardHeader title="🔌 API Overview" />
              <CardContent>
                <div className="space-y-4">
                  <p className="text-cyber-muted">
                    O Blueprint Blog v2 oferece uma API RESTful completa:
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { method: 'GET', endpoint: '/api/posts', desc: 'Listar posts' },
                      { method: 'POST', endpoint: '/api/posts', desc: 'Criar post' },
                      { method: 'GET', endpoint: '/api/categories', desc: 'Listar categorias' },
                      { method: 'GET', endpoint: '/api/tags', desc: 'Listar tags' }
                    ].map((api, index) => (
                      <div key={index} className="p-3 rounded-lg border border-cyber-border">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className={clsx(
                            'px-2 py-1 rounded text-xs font-mono',
                            api.method === 'GET' ? 'bg-green-500/20 text-green-400' : 'bg-blue-500/20 text-blue-400'
                          )}>
                            {api.method}
                          </span>
                          <code className="text-neon-cyan text-sm">{api.endpoint}</code>
                        </div>
                        <p className="text-cyber-muted text-sm">{api.desc}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contributing */}
            <Card>
              <CardHeader title="🤝 Contribuindo" />
              <CardContent>
                <div className="space-y-4">
                  <p className="text-cyber-muted">
                    Adoraríamos sua contribuição! Aqui está como começar:
                  </p>
                  
                  <ol className="list-decimal list-inside space-y-2 text-cyber-muted">
                    <li>Faça um fork do repositório</li>
                    <li>Crie uma branch para sua feature</li>
                    <li>Faça suas alterações</li>
                    <li>Adicione testes se necessário</li>
                    <li>Envie um pull request</li>
                  </ol>

                  <div className="flex space-x-4 mt-6">
                    <a
                      href="https://github.com/blueprintblog/v2"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-neon-cyan/20 text-neon-cyan rounded-lg hover:bg-neon-cyan/30 transition-colors"
                    >
                      <Code className="w-4 h-4 mr-2" />
                      Ver no GitHub
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Docs;
