/**
 * @fileoverview Página da Equipe do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Github, Linkedin, Mail, Users } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface TeamPageProps extends BaseComponentProps {}

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  avatar: string;
  social: {
    github?: string;
    linkedin?: string;
    email?: string;
  };
}

// ============================================================================
// DADOS MOCKADOS TEMPORÁRIOS
// ============================================================================

const teamMembers: TeamMember[] = [
  {
    id: '1',
    name: 'Blueprint Team',
    role: 'Desenvolvimento Full-Stack',
    bio: 'Equipe dedicada ao desenvolvimento e manutenção do Blueprint Blog v2.',
    avatar: '/api/placeholder/150/150',
    social: {
      github: 'https://github.com/blueprintblog',
      email: '<EMAIL>'
    }
  }
];

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página da equipe com informações dos membros
 */
export const Team: React.FC<TeamPageProps> = ({ className }) => {
  const teamClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  return (
    <div className={teamClasses}>
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Users className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Nossa Equipe
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Conheça as pessoas por trás do Blueprint Blog v2
          </p>
        </div>

        {/* Status de Desenvolvimento */}
        <Card className="mb-8 border-amber-500/30 bg-amber-500/5">
          <CardHeader 
            title="🚧 Em Desenvolvimento" 
            subtitle="Esta página está sendo construída"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="text-cyber-muted">
                <p className="mb-2">Funcionalidades planejadas:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Perfis detalhados dos membros da equipe</li>
                  <li>Histórico de contribuições</li>
                  <li>Links para redes sociais e projetos</li>
                  <li>Estatísticas de atividade</li>
                  <li>Formulário de contato direto</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Membros da Equipe */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {teamMembers.map((member) => (
            <Card key={member.id} className="hover:border-neon-cyan/50 transition-colors">
              <CardContent className="p-6">
                <div className="text-center">
                  {/* Avatar */}
                  <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple p-1">
                    <div className="w-full h-full rounded-full bg-cyber-surface flex items-center justify-center">
                      <Users className="w-8 h-8 text-neon-cyan" />
                    </div>
                  </div>

                  {/* Info */}
                  <h3 className="text-xl font-bold text-cyber-text mb-1">
                    {member.name}
                  </h3>
                  <p className="text-neon-cyan font-medium mb-3">
                    {member.role}
                  </p>
                  <p className="text-cyber-muted text-sm mb-4">
                    {member.bio}
                  </p>

                  {/* Social Links */}
                  <div className="flex justify-center space-x-3">
                    {member.social.github && (
                      <a
                        href={member.social.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-lg text-cyber-muted hover:text-white hover:bg-white/10 transition-colors"
                      >
                        <Github className="w-5 h-5" />
                      </a>
                    )}
                    {member.social.linkedin && (
                      <a
                        href={member.social.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-lg text-cyber-muted hover:text-[#0077B5] hover:bg-[#0077B5]/10 transition-colors"
                      >
                        <Linkedin className="w-5 h-5" />
                      </a>
                    )}
                    {member.social.email && (
                      <a
                        href={`mailto:${member.social.email}`}
                        className="p-2 rounded-lg text-cyber-muted hover:text-neon-cyan hover:bg-neon-cyan/10 transition-colors"
                      >
                        <Mail className="w-5 h-5" />
                      </a>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <Card>
          <CardHeader title="🤝 Junte-se à Nossa Equipe" />
          <CardContent>
            <div className="text-center">
              <p className="text-cyber-muted mb-6">
                Interessado em contribuir com o Blueprint Blog? Entre em contato conosco!
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-neon-cyan to-neon-purple text-white rounded-lg font-medium hover:scale-105 transition-transform"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Entre em Contato
                </a>
                <a
                  href="https://github.com/blueprintblog"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-6 py-3 border border-cyber-border text-cyber-text rounded-lg hover:border-neon-cyan/50 transition-colors"
                >
                  <Github className="w-4 h-4 mr-2" />
                  Ver no GitHub
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Team;
