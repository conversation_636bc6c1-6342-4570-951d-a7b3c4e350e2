/**
 * @fileoverview Página da Equipe do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Gith<PERSON>, Linkedin, Mail, Users, Globe, Twitter, Clock } from 'lucide-react';
import { useEffect } from 'react';
import { Card, CardContent, CardHeader } from '../components/ui';
import { useTeam } from '../hooks/useTeam';
import { useAnalytics } from '../hooks/useAnalytics';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================


// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página da equipe com informações dos membros
 */
export const Team: React.FC<TeamPageProps> = ({ className }) => {
  const { teamMembers, loading, error } = useTeam();
  const { trackEvent } = useAnalytics();

  const teamClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  // Track page view
  useEffect(() => {
    trackEvent('page_view', {
      page_title: 'Team',
      page_location: '/team'
    });
  }, [trackEvent]);

  const getSocialIcon = (platform: string) => {
    switch (platform) {
      case 'github':
        return <Github className="w-5 h-5" />;
      case 'linkedin':
        return <Linkedin className="w-5 h-5" />;
      case 'twitter':
        return <Twitter className="w-5 h-5" />;
      case 'website':
        return <Globe className="w-5 h-5" />;
      default:
        return <Mail className="w-5 h-5" />;
    }
  };

  const getSocialColor = (platform: string) => {
    switch (platform) {
      case 'github':
        return 'hover:text-white hover:bg-white/10';
      case 'linkedin':
        return 'hover:text-[#0077B5] hover:bg-[#0077B5]/10';
      case 'twitter':
        return 'hover:text-[#1DA1F2] hover:bg-[#1DA1F2]/10';
      case 'website':
        return 'hover:text-neon-purple hover:bg-neon-purple/10';
      default:
        return 'hover:text-neon-cyan hover:bg-neon-cyan/10';
    }
  };

  return (
    <div className={teamClasses}>
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Users className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Nossa Equipe
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Conheça as pessoas por trás do Blueprint Blog v2
          </p>
        </div>

        {/* Status de Carregamento */}
        {loading && (
          <Card className="mb-8">
            <CardContent className="p-8 text-center">
              <Clock className="w-8 h-8 text-neon-cyan mx-auto mb-4 animate-spin" />
              <p className="text-cyber-muted">Carregando equipe...</p>
            </CardContent>
          </Card>
        )}

        {/* Erro */}
        {error && (
          <Card className="mb-8 border-red-500/30 bg-red-500/5">
            <CardContent className="p-6">
              <p className="text-red-400">❌ Erro ao carregar equipe: {error}</p>
            </CardContent>
          </Card>
        )}

        {/* Membros da Equipe */}
        {!loading && !error && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {teamMembers.length > 0 ? teamMembers.map((member) => (
              <Card key={member.id} className="hover:border-neon-cyan/50 transition-colors">
                <CardContent className="p-6">
                  <div className="text-center">
                    {/* Avatar */}
                    <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple p-1">
                      {member.avatar ? (
                        <img
                          src={member.avatar}
                          alt={member.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full rounded-full bg-cyber-surface flex items-center justify-center">
                          <Users className="w-8 h-8 text-neon-cyan" />
                        </div>
                      )}
                    </div>

                    {/* Info */}
                    <h3 className="text-xl font-bold text-cyber-text mb-1">
                      {member.name}
                    </h3>
                    <p className="text-neon-cyan font-medium mb-2">
                      {member.role}
                    </p>

                    {/* Estatísticas */}
                    <div className="flex justify-center space-x-4 mb-3 text-xs">
                      <span className="text-cyber-muted">
                        📝 {member.postsCount} posts
                      </span>
                      {member.lastPostDate && (
                        <span className="text-cyber-muted">
                          📅 {new Date(member.lastPostDate).toLocaleDateString('pt-BR')}
                        </span>
                      )}
                    </div>

                    <p className="text-cyber-muted text-sm mb-4">
                      {member.bio}
                    </p>

                    {/* Social Links */}
                    <div className="flex justify-center space-x-2">
                      {Object.entries(member.social).map(([platform, url]) => {
                        if (!url) return null;
                        return (
                          <a
                            key={platform}
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={clsx(
                              'p-2 rounded-lg text-cyber-muted transition-colors',
                              getSocialColor(platform)
                            )}
                            title={platform}
                          >
                            {getSocialIcon(platform)}
                          </a>
                        );
                      })}
                      {member.email && (
                        <a
                          href={`mailto:${member.email}`}
                          className="p-2 rounded-lg text-cyber-muted hover:text-neon-cyan hover:bg-neon-cyan/10 transition-colors"
                          title="Email"
                        >
                          <Mail className="w-5 h-5" />
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )) : (
              <div className="col-span-full text-center py-12">
                <Users className="w-16 h-16 text-cyber-muted mx-auto mb-4" />
                <p className="text-cyber-muted">Nenhum membro da equipe encontrado.</p>
              </div>
            )}
          </div>
        )}

        {/* Call to Action */}
        <Card>
          <CardHeader title="🤝 Junte-se à Nossa Equipe" />
          <CardContent>
            <div className="text-center">
              <p className="text-cyber-muted mb-6">
                Interessado em contribuir com o Blueprint Blog? Entre em contato conosco!
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-neon-cyan to-neon-purple text-white rounded-lg font-medium hover:scale-105 transition-transform"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Entre em Contato
                </a>
                <a
                  href="https://github.com/blueprintblog"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-6 py-3 border border-cyber-border text-cyber-text rounded-lg hover:border-neon-cyan/50 transition-colors"
                >
                  <Github className="w-4 h-4 mr-2" />
                  Ver no GitHub
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Team;
