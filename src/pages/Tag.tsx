/**
 * @fileoverview Página de Tag do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router';
import { <PERSON><PERSON>, Card, CardContent, Input } from '../components/ui';
import AuthorAvatar from '../components/ui/AuthorAvatar';
import { PostGrid } from '../components/ui/PostGrid';
import { usePosts } from '../hooks/usePosts';
import { useTags } from '../hooks/useTags';
import type { Post } from '../types';

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Normaliza string para slug consistente
 */
const normalizeSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
    .replace(/\s+/g, '-') // Substitui espaços por hífens
    .replace(/-+/g, '-') // Remove hífens duplicados
    .trim();
};

// ============================================================================
// INTERFACES
// ============================================================================

interface TagPageProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de tag com posts relacionados
 */
export const TagPage: React.FC<TagPageProps> = ({ className }) => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const postsPerPage = 6;

  // Hooks do Supabase
  const { posts: allPosts, loading: postsLoading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
  });
  const { tags, loading: tagsLoading } = useTags();

  // Filtrar posts por tag
  const posts = useMemo(() => {
    if (!slug || !allPosts.length) return [];

    // Converte slug para nome da tag para comparação
    const currentTag = tags.find((t: any) => t.slug === slug);
    const tagName = currentTag?.name;

    if (!tagName) return [];

    return allPosts.filter((post: Post) => {
      if (!post.tags || !Array.isArray(post.tags)) return false;

      return post.tags.some((tag: any) => {
        // Se tag é string (nome), compara diretamente e por slug normalizado
        if (typeof tag === 'string') {
          return tag === tagName || normalizeSlug(tag) === slug;
        }
        // Se tag é objeto, compara slug ou nome
        return (
          tag.slug === slug ||
          tag.name === tagName ||
          normalizeSlug(tag.name || '') === slug
        );
      });
    });
  }, [allPosts, slug, tags]);

  // Tag atual
  const currentTag = tags.find((t: any) => t.slug === slug);
  const loading = postsLoading || tagsLoading;

  // Função para navegar para o post
  const handlePostClick = (post: Post) => {
    navigate(`/post/${post.slug}`);
  };

  // Debug: Log dos dados carregados
  console.log('🏷️ [TagPage] Dados carregados:', {
    slug,
    postsCount: posts.length,
    allPostsCount: allPosts.length,
    tagsCount: tags.length,
    currentTag: currentTag?.name,
    loading,
    firstPostTags: allPosts[0]?.tags,
    tagsStructure: allPosts.slice(0, 2).map((post) => ({
      title: post.title,
      tags: post.tags,
      tagsType: typeof post.tags?.[0],
    })),
  });

  // Filtra posts baseado na busca
  const filteredPosts = posts.filter(
    (post) =>
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Paginação
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(
    startIndex,
    startIndex + postsPerPage
  );

  // Reset página quando busca muda
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando tag...</p>
        </div>
      </div>
    );
  }

  if (!currentTag && !loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-cyber-text mb-4">404</h1>
          <p className="text-cyber-muted mb-6">Tag não encontrada</p>
          <Button onClick={() => navigate('/blog')}>Voltar ao Blog</Button>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('space-y-8', className)}>
      {/* Breadcrumb */}
      <nav>
        <div className="flex items-center space-x-2 text-sm text-cyber-muted">
          <a href="/" className="hover:text-neon-cyan transition-colors">
            Home
          </a>
          <span>/</span>
          <a href="/blog" className="hover:text-neon-cyan transition-colors">
            Blog
          </a>
          <span>/</span>
          <span className="text-cyber-text">Tags</span>
          <span>/</span>
          <span className="text-cyber-text">#{currentTag?.name || slug}</span>
        </div>
      </nav>

      {/* Tag Header */}
      <header className="text-center">
        <div className="mb-6">
          <span className="inline-block px-6 py-3 rounded-full text-2xl font-bold bg-neon-purple/20 text-neon-purple border-2 border-neon-purple/30">
            #{currentTag?.name || slug}
          </span>
        </div>

        <h1 className="text-4xl md:text-5xl font-bold text-cyber-text mb-4">
          Posts sobre #{currentTag?.name || slug}
        </h1>

        <p className="text-xl text-cyber-muted max-w-2xl mx-auto mb-6">
          Explore todos os artigos relacionados à tag{' '}
          <strong>#{currentTag?.name || slug}</strong>
        </p>

        <div className="flex items-center justify-center space-x-6 text-sm text-cyber-muted">
          <div className="flex items-center space-x-2">
            <span>📝</span>
            <span>
              {posts.length} post{posts.length !== 1 ? 's' : ''}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span>👁️</span>
            <span>
              {posts.reduce((acc, post) => acc + post.views, 0)} visualizações
            </span>
          </div>
        </div>
      </header>

      {/* Search */}
      <section className="max-w-md mx-auto">
        <Input
          type="search"
          placeholder={`Buscar em #${currentTag?.name || slug}...`}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          leftIcon={
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          }
          fullWidth
        />

        {filteredPosts.length !== posts.length && (
          <div className="mt-2 text-sm text-cyber-muted text-center">
            {filteredPosts.length} de {posts.length} posts
          </div>
        )}
      </section>

      {/* Posts Grid */}
      <section>
        <PostGrid
          posts={paginatedPosts}
          loading={loading}
          layout="grid"
          columns={2}
          showAuthor={true}
          showDate={true}
          showExcerpt={true}
          showTags={true}
          showViews={true}
          showReadTime={true}
          showInteractions={true}
          onPostClick={handlePostClick}
          emptyMessage="Nenhum post encontrado para esta tag"
          emptyIcon="🏷️"
        />
      </section>

      {/* Pagination */}
      {totalPages > 1 && (
        <section className="flex justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}>
              ← Anterior
            </Button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={clsx(
                  'w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200',
                  currentPage === page
                    ? 'bg-neon-purple text-cyber-bg'
                    : 'bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border'
                )}>
                {page}
              </button>
            ))}

            <Button
              variant="secondary"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}>
              Próxima →
            </Button>
          </div>
        </section>
      )}

      {/* Tag Cloud */}
      <section className="bg-cyber-surface rounded-lg p-6 border border-cyber-border">
        <h2 className="text-xl font-bold text-cyber-text mb-4">
          🏷️ Outras Tags Populares
        </h2>
        <div className="flex flex-wrap gap-3">
          {tags
            .filter((t) => t.slug !== slug)
            .slice(0, 15)
            .map((otherTag) => (
              <a
                key={otherTag.id}
                href={`/tag/${otherTag.slug}`}
                className="px-3 py-2 rounded-lg bg-cyber-bg text-cyber-muted hover:text-neon-purple border border-cyber-border hover:border-neon-purple transition-all duration-200">
                #{otherTag.name}
              </a>
            ))}
        </div>
      </section>
    </div>
  );
};

export default TagPage;
