/**
 * @fileoverview Página Sobre do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React from 'react';
import { clsx } from 'clsx';
import {
  FiBook,
  FiCode,
  FiCoffee,
  FiGithub,
  FiHeart,
  FiLinkedin,
  FiMail,
  FiMapPin,
  FiTarget,
  FiTrendingUp,
  FiTwitter,
  FiUser,
} from 'react-icons/fi';
import {
  SiDocker,
  SiFigma,
  SiGit,
  SiNodedotjs,
  SiPostgresql,
  SiReact,
  SiTailwindcss,
  SiTypescript,
  SiVercel,
} from 'react-icons/si';
import { JsonLdSchema, SEOHead } from '../components/seo';
import { useMobileDetection } from '../hooks/useMobileDetection';
import useSEO from '../hooks/useSEO';

// ============================================================================
// INTERFACES
// ============================================================================

interface AboutProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página Sobre do Blueprint Blog
 */
export const About: React.FC<AboutProps> = () => {
  const { isMobile } = useMobileDetection();

  // SEO para página sobre
  const seoData = useSEO({
    pageType: 'page',
    customTitle: 'Sobre | Blueprint Blog',
    customDescription:
      'Conheça a história do Blueprint Blog e seu fundador. Democratizando conhecimento técnico para a comunidade de desenvolvedores.',
    customKeywords: [
      'sobre',
      'blueprint blog',
      'história',
      'fundador',
      'missão',
      'valores',
      'tecnologia',
    ],
  });

  const skills = [
    { name: 'React', icon: SiReact, color: 'text-blue-400' },
    { name: 'TypeScript', icon: SiTypescript, color: 'text-blue-500' },
    { name: 'Node.js', icon: SiNodedotjs, color: 'text-green-400' },
    { name: 'Tailwind', icon: SiTailwindcss, color: 'text-cyan-400' },
    { name: 'PostgreSQL', icon: SiPostgresql, color: 'text-blue-600' },
    { name: 'Vercel', icon: SiVercel, color: 'text-white' },
    { name: 'Docker', icon: SiDocker, color: 'text-blue-500' },
    { name: 'Git', icon: SiGit, color: 'text-orange-500' },
    { name: 'Figma', icon: SiFigma, color: 'text-purple-500' },
  ];

  // Stats para uso futuro quando tivermos números reais
  /*
  const stats = [
    { label: 'Artigos Publicados', value: '150+', icon: FiBook },
    { label: 'Devs Alcançados', value: '50K+', icon: FiUsers },
    { label: 'Libraries Documentadas', value: '200+', icon: FiCode },
    { label: 'Projetos Open Source', value: '25+', icon: FiGithub },
    { label: 'Anos Ativo', value: '2', icon: FiCalendar },
    { label: 'Países Alcançados', value: '35', icon: FiGlobe },
  ];
  */

  const socialLinks = [
    {
      name: 'LinkedIn',
      url: 'https://www.linkedin.com/in/genildo-cerqueira-91888786/',
      icon: FiLinkedin,
      color: 'text-blue-500 hover:text-blue-400',
    },
    {
      name: 'GitHub',
      url: 'https://github.com/Genildocs?tab=repositories',
      icon: FiGithub,
      color: 'text-white hover:text-gray-300',
    },
    {
      name: 'Twitter',
      url: 'https://x.com/blue_printblog',
      icon: FiTwitter,
      color: 'text-sky-500 hover:text-sky-400',
    },
  ];

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="page"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Sobre', url: '/about' },
        ]}
      />

      <div className="min-h-screen bg-cyber-bg py-8 mobile-xs:py-6 sm:py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section - Mobile Optimized */}
          <div className="text-center mb-12 mobile-xs:mb-8 sm:mb-16">
            <div className="flex justify-center mb-4 mobile-xs:mb-3 sm:mb-6">
              <div className="relative">
                <div className={clsx(
                  'bg-neon-cyan/20 rounded-full border border-neon-cyan',
                  'p-4 mobile-xs:p-3 sm:p-6'
                )}>
                  <FiUser className={clsx(
                    'text-neon-cyan',
                    'w-12 h-12 mobile-xs:w-10 mobile-xs:h-10 sm:w-16 sm:h-16'
                  )} />
                </div>
                <div className={clsx(
                  'absolute -top-2 -right-2 bg-cyber-bg rounded-full border border-neon-cyan',
                  'p-1.5 mobile-xs:p-1 sm:p-2'
                )}>
                  <FiHeart className={clsx(
                    'text-red-400',
                    'w-4 h-4 mobile-xs:w-3 mobile-xs:h-3 sm:w-6 sm:h-6'
                  )} />
                </div>
              </div>
            </div>
            <h1 className="text-3xl-mobile sm:text-4xl md:text-6xl font-bold text-white mb-3 mobile-xs:mb-2 sm:mb-4">
              Sobre o <span className="text-neon-cyan">Blueprint</span>
            </h1>
            <p className="text-cyber-muted text-base-mobile sm:text-xl max-w-3xl mx-auto leading-relaxed">
              Democratizando conhecimento técnico e agregando valor real à
              comunidade de desenvolvedores
            </p>
          </div>

          {/* Mission Section - Mobile Optimized */}
          <section className="mb-12 mobile-xs:mb-8 sm:mb-16">
            <div className={clsx(
              'bg-cyber-surface/50 backdrop-blur-sm rounded-2xl border border-cyber-border',
              'p-6 mobile-xs:p-4 sm:p-8 md:p-12'
            )}>
              <div className={clsx(
                'grid gap-6 mobile-xs:gap-4 sm:gap-8 items-center',
                'grid-cols-1 lg:grid-cols-2'
              )}>
                <div>
                  <p className={clsx(
                    'text-cyber-text leading-relaxed mb-4 mobile-xs:mb-3 sm:mb-6',
                    'text-sm-mobile sm:text-base lg:text-lg'
                  )}>
                    O <strong className="text-neon-cyan">Blueprint Blog</strong>{' '}
                    nasceu com uma missão clara: democratizar o conhecimento
                    técnico e agregar valor real à comunidade de
                    desenvolvedores. Acreditamos que a tecnologia deve ser
                    acessível a todos, independentemente do nível de
                    experiência.
                  </p>
                  <div className={clsx(
                    'bg-neon-cyan/10 rounded-lg border border-neon-cyan/30',
                    'p-4 mobile-xs:p-3 sm:p-6'
                  )}>
                    <h3 className="text-white font-semibold mb-2 mobile-xs:mb-1.5 sm:mb-3 text-sm-mobile sm:text-base">
                      Nossos Valores:
                    </h3>
                    <ul className="text-cyber-text space-y-2">
                      <li>
                        ✓ <strong>Conhecimento acessível</strong> para todos os
                        níveis
                      </li>
                      <li>
                        ✓ <strong>Qualidade técnica</strong> sem sacrificar
                        clareza
                      </li>
                      <li>
                        ✓ <strong>Comunidade colaborativa</strong> e inclusiva
                      </li>
                      <li>
                        ✓ <strong>Inovação constante</strong> e aprendizado
                        contínuo
                      </li>
                    </ul>
                  </div>
                </div>

                <div className={clsx(
                  'grid gap-3 mobile-xs:gap-2 sm:gap-4',
                  // Mobile: 1 coluna, Tablet+: 2 colunas
                  'grid-cols-1 mobile-lg:grid-cols-2'
                )}>
                  <div className={clsx(
                    'bg-cyber-bg/50 rounded-lg border border-green-500/30 text-center',
                    'p-4 mobile-xs:p-3 sm:p-6'
                  )}>
                    <FiBook className={clsx(
                      'text-green-400 mx-auto mb-2 mobile-xs:mb-1.5 sm:mb-3',
                      'w-6 h-6 mobile-xs:w-5 mobile-xs:h-5 sm:w-8 sm:h-8'
                    )} />
                    <h4 className="text-white font-semibold mb-1.5 mobile-xs:mb-1 sm:mb-2 text-sm-mobile sm:text-base">
                      Tutoriais
                    </h4>
                    <p className="text-cyber-muted text-xs-mobile sm:text-sm">
                      Step-by-step detalhados com código funcional
                    </p>
                  </div>
                  <div className={clsx(
                    'bg-cyber-bg/50 rounded-lg border border-blue-500/30 text-center',
                    'p-4 mobile-xs:p-3 sm:p-6'
                  )}>
                    <FiTrendingUp className={clsx(
                      'text-blue-400 mx-auto mb-2 mobile-xs:mb-1.5 sm:mb-3',
                      'w-6 h-6 mobile-xs:w-5 mobile-xs:h-5 sm:w-8 sm:h-8'
                    )} />
                    <h4 className="text-white font-semibold mb-1.5 mobile-xs:mb-1 sm:mb-2 text-sm-mobile sm:text-base">
                      Notícias
                    </h4>
                    <p className="text-cyber-muted text-xs-mobile sm:text-sm">
                      Últimas tendências do mundo tech
                    </p>
                  </div>
                  <div className="bg-cyber-bg/50 p-6 rounded-lg border border-purple-500/30 text-center">
                    <FiCode className="w-8 h-8 text-purple-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Reviews</h4>
                    <p className="text-cyber-muted text-sm">
                      Análises profundas de ferramentas e frameworks
                    </p>
                  </div>
                  <div className="bg-cyber-bg/50 p-6 rounded-lg border border-yellow-500/30 text-center">
                    <FiTarget className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Cases</h4>
                    <p className="text-cyber-muted text-sm">
                      Estudos de caso reais e práticos
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Stats Section - Desabilitado até termos números reais */}
          {/*
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-white text-center mb-12">
              📊 Nossos <span className="text-neon-cyan">Números</span>
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="bg-cyber-surface/30 p-6 rounded-lg border border-cyber-border text-center">
                  <stat.icon className="w-8 h-8 text-neon-cyan mx-auto mb-3" />
                  <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-cyber-muted text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </section>
          */}

          {/* Tech Stack - Mobile Optimized */}
          <section className="mb-12 mobile-xs:mb-8 sm:mb-16">
            <h2 className="text-2xl-mobile sm:text-3xl font-bold text-white text-center mb-8 mobile-xs:mb-6 sm:mb-12">
              🛠️ Nossa <span className="text-neon-cyan">Stack</span>
            </h2>
            <div className={clsx(
              'grid gap-4 mobile-xs:gap-3 sm:gap-6',
              // Mobile: 3 colunas, Tablet: 5 colunas, Desktop: 9 colunas
              'grid-cols-3 tablet:grid-cols-5 lg:grid-cols-9'
            )}>
              {skills.map((skill, index) => (
                <div
                  key={index}
                  className={clsx(
                    'bg-cyber-surface/30 rounded-lg border border-cyber-border text-center',
                    'group hover:border-neon-cyan/50 transition-colors',
                    'p-3 mobile-xs:p-2 sm:p-4'
                  )}>
                  <skill.icon
                    className={clsx(
                      `mx-auto mb-1.5 mobile-xs:mb-1 sm:mb-2 ${skill.color}`,
                      'group-hover:scale-110 transition-transform',
                      'w-6 h-6 mobile-xs:w-5 mobile-xs:h-5 sm:w-8 sm:h-8'
                    )}
                  />
                  <div className="text-cyber-text font-medium text-xs-mobile sm:text-sm">
                    {skill.name}
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* Contact Section - Mobile Optimized */}
          <section className="mb-12 mobile-xs:mb-8 sm:mb-16">
            <div className={clsx(
              'bg-gradient-to-r from-neon-cyan/10 to-purple-500/10 backdrop-blur-sm',
              'rounded-2xl border border-neon-cyan/30',
              'p-6 mobile-xs:p-4 sm:p-8 md:p-12'
            )}>
              <div className="text-center mb-6 mobile-xs:mb-4 sm:mb-8">
                <FiMail className={clsx(
                  'text-neon-cyan mx-auto mb-3 mobile-xs:mb-2 sm:mb-4',
                  'w-10 h-10 mobile-xs:w-8 mobile-xs:h-8 sm:w-12 sm:h-12'
                )} />
                <h2 className="text-2xl-mobile sm:text-3xl font-bold text-white mb-3 mobile-xs:mb-2 sm:mb-4">
                  💬 Vamos Conversar
                </h2>
                <p className="text-cyber-muted text-sm-mobile sm:text-base">
                  Tem alguma dúvida, sugestão ou quer colaborar? Entre em
                  contato!
                </p>
              </div>

              <div className={clsx(
                'grid gap-6 mobile-xs:gap-4 sm:gap-8',
                'grid-cols-1 lg:grid-cols-2'
              )}>
                {/* Social Links - Mobile Optimized */}
                <div>
                  <h3 className="text-lg-mobile sm:text-xl font-semibold text-white mb-4 mobile-xs:mb-3 sm:mb-6">
                    🤝 Redes Sociais
                  </h3>
                  <div className="space-y-3 mobile-xs:space-y-2 sm:space-y-4">
                    {socialLinks.map((social, index) => (
                      <a
                        key={index}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={clsx(
                          'flex items-center bg-cyber-bg/50 rounded-lg border border-cyber-surface',
                          'hover:border-neon-cyan/50 transition-colors group',
                          'p-3 mobile-xs:p-2.5 sm:p-4',
                          'gap-3 mobile-xs:gap-2 sm:gap-4',
                          social.color
                        )}>
                        <social.icon className={clsx(
                          'group-hover:scale-110 transition-transform',
                          'w-5 h-5 mobile-xs:w-4 mobile-xs:h-4 sm:w-6 sm:h-6'
                        )} />
                        <span className="font-medium text-sm-mobile sm:text-base">
                          {social.name}
                        </span>
                        {!isMobile && (
                          <span className="text-cyber-muted text-sm ml-auto">
                            @blue_printblog
                          </span>
                        )}
                      </a>
                    ))}
                  </div>
                </div>

                {/* Contact Info */}
                <div>
                  <h3 className="text-xl font-semibold text-white mb-6">
                    📧 Contato Direto
                  </h3>
                  <div className="space-y-4">
                    <div className="p-4 bg-cyber-bg/50 rounded-lg border border-cyber-surface">
                      <div className="flex items-center gap-3 mb-2">
                        <FiMail className="w-5 h-5 text-neon-cyan" />
                        <span className="text-white font-medium">Email</span>
                      </div>
                      <p className="text-cyber-muted">
                        <EMAIL>
                      </p>
                    </div>

                    <div className="p-4 bg-cyber-bg/50 rounded-lg border border-cyber-surface">
                      <div className="flex items-center gap-3 mb-2">
                        <FiMapPin className="w-5 h-5 text-neon-cyan" />
                        <span className="text-white font-medium">
                          Localização
                        </span>
                      </div>
                      <p className="text-cyber-muted">
                        Casimiro de Abreu, Rio de Janeiro, Brasil
                      </p>
                    </div>
                  </div>

                  <div className="mt-6">
                    <a
                      href="mailto:<EMAIL>"
                      className="inline-block border border-neon-cyan text-neon-cyan font-semibold px-8 py-3 rounded-lg hover:bg-neon-cyan/10 transition-colors">
                      Enviar Email
                    </a>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-6 border-t border-cyber-surface">
                <p className="text-cyber-muted text-sm text-center">
                  <FiCoffee className="inline w-4 h-4 mr-1" />
                  Feito com 💙 e muito ☕ por um desenvolvedor apaixonado por
                  tecnologia e comunidade.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </>
  );
};

export default About;
