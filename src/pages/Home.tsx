/**
 * @fileoverview Página Home do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useNavigate } from 'react-router';
import { JsonLdSchema, SEOHead } from '../components/seo';
import { Button, HeroPost, PostSlider, TopOfMonth } from '../components/ui';
import { TopFixBarNewPost } from '../components/layout';
import { usePosts } from '../hooks/usePosts';
import { useMobileDetection } from '../hooks/useMobileDetection';
import useSEO from '../hooks/useSEO';
import type { Post } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface HomeProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página inicial do blog com hero section e posts em destaque
 */
export const Home: React.FC<HomeProps> = ({ className }) => {
  const navigate = useNavigate();
  const { isMobile } = useMobileDetection();

  // SEO para página inicial
  const seoData = useSEO({
    pageType: 'home',
    customTitle: 'Blueprint Blog - Tecnologia, Desenvolvimento e Inovação',
    customDescription:
      'Descubra os melhores artigos sobre tecnologia, programação, design e inovação. Conteúdo exclusivo para desenvolvedores e entusiastas tech.',
    customKeywords: [
      'tecnologia',
      'programação',
      'desenvolvimento',
      'design',
      'inovação',
      'blog tech',
      'artigos tecnologia',
    ],
  });

  // Buscar posts do Supabase
  const { posts, loading, error } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
    limit: 20,
  });

  // Filtrar posts para diferentes seções
  const featuredPosts = posts.filter((post: Post) => post.featured);
  const recentPosts = posts.slice(0, 6);
  const topPosts = posts.slice(0, 5); // Para TopOfMonth
  const heroPost = featuredPosts[0] || posts[0]; // Post principal (featured ou mais recente)

  // 🔍 DEBUG: Log dos posts carregados
  console.log('🏠 [Home] Posts carregados:', {
    total: posts.length,
    featured: featuredPosts.length,
    heroPost: heroPost?.title,
    recentPosts: recentPosts.length,
    topPosts: topPosts.length,
  });

  // Função para navegar para o post
  const handlePostClick = (post: Post) => {
    navigate(`/post/${post.slug}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando posts do Supabase...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-cyber-text mb-2">
            Erro ao carregar posts
          </h2>
          <p className="text-cyber-muted mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Tentar Novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema type="home" breadcrumbs={[{ name: 'Home', url: '/' }]} />

      {/* TopFixBar para Mobile - Novo Post */}
      {isMobile && recentPosts[0] && (
        <TopFixBarNewPost
          postTitle={recentPosts[0].title}
          href={`/post/${recentPosts[0].slug}`}
          dismissible={true}
          autoHide={true}
        />
      )}

      <div className={clsx('space-y-12', className)}>
        {/* Above the Fold - Hero Post + Top do Mês */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Hero Post - 2/3 width */}
          {heroPost && (
            <div className="lg:col-span-2">
              <HeroPost
                post={heroPost}
                height="xl"
                onPostClick={handlePostClick}
              />
            </div>
          )}

          {/* Top of Month - 1/3 width */}
          {topPosts.length > 0 && (
            <div className="lg:col-span-1">
              <TopOfMonth
                posts={topPosts}
                title="🔥 Top do Mês"
                subtitle="Posts mais visualizados"
                layout="vertical"
                onPostClick={handlePostClick}
              />
            </div>
          )}
        </div>

        {/* Below the Fold - Recent Posts Slider */}
        {recentPosts.length > 0 && (
          <PostSlider
            posts={recentPosts}
            title="📝 Posts Recentes"
            subtitle="Continue lendo"
            slidesToShow={3}
            autoPlay={true}
            autoPlayInterval={5000}
            onPostClick={handlePostClick}
          />
        )}

        {/* Newsletter CTA */}
        <section className="bg-gradient-cyber rounded-lg p-8 text-center shadow-2xl shadow-cyber-bg/40">
          <h2 className="text-3xl font-bold text-cyber-text mb-4">
            📧 Fique por Dentro das Novidades
          </h2>
          <p className="text-cyber-muted mb-6 max-w-2xl mx-auto">
            Receba semanalmente os melhores artigos sobre tecnologia,
            desenvolvimento e inovação diretamente no seu email.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="<EMAIL>"
              className="flex-1 px-4 py-2 rounded-lg bg-cyber-surface border border-cyber-border text-cyber-text placeholder:text-cyber-muted focus:outline-none focus:border-neon-cyan"
            />
            <Button>Inscrever-se</Button>
          </div>
        </section>
      </div>
    </>
  );
};

export default Home;
