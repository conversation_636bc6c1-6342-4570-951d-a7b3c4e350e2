/**
 * @fileoverview Página de Blog (lista de posts) do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { JsonLdSchema, SEOHead } from '../components/seo';
import { Button, Input } from '../components/ui';
import { PostGrid } from '../components/ui/PostGrid';
import { TopFixBarAnnouncement } from '../components/layout';
import { usePosts } from '../hooks/usePosts';
import { useMobileDetection } from '../hooks/useMobileDetection';
import useSEO from '../hooks/useSEO';
import { supabase } from '../lib/supabaseClient';
import type { Category } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface BlogProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de blog com lista de posts e filtros
 */
export const Blog: React.FC<BlogProps> = ({ className }) => {
  const navigate = useNavigate();
  const { isMobile } = useMobileDetection();
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);

  const postsPerPage = 6;

  // SEO para página de blog
  const selectedCategoryData = categories.find(
    (cat) => cat.id === selectedCategory
  );
  const seoData = useSEO({
    pageType: 'blog',
    customTitle: searchQuery
      ? `Busca: "${searchQuery}" | Blog Blueprint`
      : selectedCategoryData
      ? `${selectedCategoryData.name} | Blog Blueprint`
      : 'Blog | Blueprint - Artigos sobre Tecnologia',
    customDescription: searchQuery
      ? `Resultados da busca por "${searchQuery}" no Blueprint Blog. Artigos sobre tecnologia, desenvolvimento e inovação.`
      : selectedCategoryData
      ? `Artigos sobre ${selectedCategoryData.name.toLowerCase()} no Blueprint Blog. ${
          selectedCategoryData.description ||
          'Conteúdo especializado e atualizado.'
        }`
      : 'Explore todos os artigos do Blueprint Blog sobre tecnologia, programação, design e inovação digital.',
    customKeywords: [
      'blog',
      'artigos',
      'tecnologia',
      'programação',
      ...(searchQuery ? [searchQuery] : []),
      ...(selectedCategoryData
        ? [selectedCategoryData.name.toLowerCase()]
        : []),
    ],
  });

  // Usar hook real do Supabase para posts
  const { posts, loading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
  });

  // Carrega categorias do Supabase
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('categories')
          .select('*')
          .order('name');

        if (categoriesError) {
          console.error('Erro ao carregar categorias:', categoriesError);
          return;
        }

        // Calcular postsCount para cada categoria
        const categoriesWithCount = (categoriesData || []).map((category) => ({
          ...category,
          postsCount: posts.filter((post) => post.category.id === category.id)
            .length,
        }));

        setCategories(categoriesWithCount);
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      }
    };

    if (posts.length > 0) {
      loadCategories();
    }
  }, [posts]);

  // Filtra posts baseado na busca e categoria
  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.author.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      selectedCategory === 'all' || post.category.id === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Paginação
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(
    startIndex,
    startIndex + postsPerPage
  );

  // Reset página quando filtros mudam
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory]);

  // Função para navegar para o post
  const handlePostClick = (post: any) => {
    navigate(`/post/${post.slug}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando posts...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="blog"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Blog', url: '/blog' },
        ]}
      />

      {/* TopFixBar para Mobile - Anúncio */}
      {isMobile && (
        <TopFixBarAnnouncement
          dismissible={true}
          autoHide={true}
        />
      )}

      <div className={clsx('space-y-8', className)}>
        {/* Header - Mobile Optimized */}
        <header className="text-center">
          <h1 className="text-3xl-mobile sm:text-4xl md:text-5xl font-bold text-cyber-text mb-3 sm:mb-4">
            📝 Blog
          </h1>
          <p className="text-base-mobile sm:text-xl text-cyber-muted max-w-2xl mx-auto">
            Artigos sobre tecnologia, desenvolvimento e inovação digital
          </p>
        </header>

        {/* Filters - Mobile Optimized */}
        <section className={clsx(
          'bg-cyber-surface rounded-lg border border-cyber-border',
          'p-4 mobile-xs:p-3 sm:p-6'
        )}>
          <div className={clsx(
            'flex flex-col gap-4 items-stretch',
            'sm:flex-row sm:items-center',
            'lg:gap-6'
          )}>
            {/* Search */}
            <div className="flex-1 w-full">
              <Input
                type="search"
                placeholder={isMobile ? "Buscar..." : "Buscar posts..."}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                }
                fullWidth
                className="input-no-zoom-mobile"
              />
            </div>

            {/* Category Filter - Mobile Scroll */}
            <div className={clsx(
              'w-full',
              // Mobile: scroll horizontal
              'overflow-x-auto scrollbar-hide',
              // Desktop: flex wrap
              'sm:overflow-x-visible'
            )}>
              <div className={clsx(
                'flex gap-2',
                // Mobile: nowrap para scroll horizontal
                'whitespace-nowrap mobile-xs:gap-1.5',
                // Desktop: wrap normal
                'sm:flex-wrap sm:whitespace-normal'
              )}>
                <button
                  onClick={() => setSelectedCategory('all')}
                  className={clsx(
                    'px-3 py-2 mobile-xs:px-2.5 mobile-xs:py-1.5 rounded-lg font-medium transition-all duration-200 flex-shrink-0',
                    'text-xs-mobile sm:text-sm',
                    selectedCategory === 'all'
                      ? 'bg-neon-cyan text-cyber-bg'
                      : 'bg-cyber-bg text-cyber-muted hover:text-cyber-text border border-cyber-border'
                  )}>
                  Todas
                </button>
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={clsx(
                      'px-3 py-2 mobile-xs:px-2.5 mobile-xs:py-1.5 rounded-lg font-medium transition-all duration-200',
                      'flex items-center space-x-1.5 mobile-xs:space-x-1 flex-shrink-0',
                      'text-xs-mobile sm:text-sm',
                      selectedCategory === category.id
                        ? 'text-cyber-bg border'
                        : 'bg-cyber-bg text-cyber-muted hover:text-cyber-text border border-cyber-border'
                    )}
                    style={{
                      backgroundColor:
                        selectedCategory === category.id
                          ? category.color
                          : undefined,
                      borderColor:
                        selectedCategory === category.id
                          ? category.color
                          : undefined,
                    }}>
                    <div
                      className="w-1.5 h-1.5 mobile-xs:w-1 mobile-xs:h-1 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="truncate max-w-[80px] mobile-xs:max-w-[60px]">
                      {category.name}
                    </span>
                    {!isMobile && (
                      <span className="text-xs opacity-75">
                        ({category.postsCount})
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Results Info */}
          <div className="mt-4 text-sm text-cyber-muted">
            {filteredPosts.length === 0
              ? 'Nenhum post encontrado'
              : `${filteredPosts.length} post${
                  filteredPosts.length !== 1 ? 's' : ''
                } encontrado${filteredPosts.length !== 1 ? 's' : ''}`}
          </div>
        </section>

        {/* Posts Grid - Mobile Optimized */}
        <section>
          <PostGrid
            posts={paginatedPosts}
            loading={loading}
            layout={isMobile ? "mobile" : "grid"}
            columns={isMobile ? 1 : 3}
            mobileOptimized={true}
            showAuthor={true}
            showDate={true}
            showExcerpt={!isMobile} // Oculta excerpt em mobile para economia de espaço
            showViews={!isMobile} // Oculta views em mobile
            showReadTime={true}
            showInteractions={!isMobile} // Oculta interactions em mobile
            onPostClick={handlePostClick}
            emptyMessage="Nenhum post encontrado"
            emptyIcon="🔍"
          />
        </section>

        {/* Pagination - Mobile Optimized */}
        {totalPages > 1 && (
          <section className="flex justify-center">
            <div className={clsx(
              'flex items-center',
              'space-x-1.5 mobile-xs:space-x-1 sm:space-x-2'
            )}>
              <Button
                variant="secondary"
                size={isMobile ? "xs" : "sm"}
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
                className="button-text-mobile">
                {isMobile ? "←" : "← Anterior"}
              </Button>

              {/* Mobile: Mostrar apenas algumas páginas */}
              {isMobile ? (
                <>
                  {/* Primeira página */}
                  {currentPage > 2 && (
                    <>
                      <button
                        onClick={() => setCurrentPage(1)}
                        className="w-8 h-8 rounded-lg text-xs font-medium bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border">
                        1
                      </button>
                      {currentPage > 3 && <span className="text-cyber-muted">...</span>}
                    </>
                  )}

                  {/* Páginas ao redor da atual */}
                  {Array.from({ length: Math.min(3, totalPages) }, (_, i) => {
                    const page = Math.max(1, Math.min(totalPages - 2, currentPage - 1)) + i;
                    if (page > totalPages) return null;
                    return (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={clsx(
                          'w-8 h-8 rounded-lg text-xs font-medium transition-all duration-200',
                          currentPage === page
                            ? 'bg-neon-cyan text-cyber-bg'
                            : 'bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border'
                        )}>
                        {page}
                      </button>
                    );
                  })}

                  {/* Última página */}
                  {currentPage < totalPages - 1 && (
                    <>
                      {currentPage < totalPages - 2 && <span className="text-cyber-muted">...</span>}
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className="w-8 h-8 rounded-lg text-xs font-medium bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border">
                        {totalPages}
                      </button>
                    </>
                  )}
                </>
              ) : (
                /* Desktop: Mostrar todas as páginas */
                Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={clsx(
                        'w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200',
                        currentPage === page
                          ? 'bg-neon-cyan text-cyber-bg'
                          : 'bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border'
                      )}>
                      {page}
                    </button>
                  )
                )
              )}

              <Button
                variant="secondary"
                size={isMobile ? "xs" : "sm"}
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
                className="button-text-mobile">
                {isMobile ? "→" : "Próxima →"}
              </Button>
            </div>
          </section>
        )}
      </div>
    </>
  );
};

export default Blog;
