/**
 * @fileoverview Página de Blog (lista de posts) do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { JsonLdSchema, SEOHead } from '../components/seo';
import { Button, Card, CardContent, Input } from '../components/ui';
import { PostGrid } from '../components/ui/PostGrid';
import { usePosts } from '../hooks/usePosts';
import useSEO from '../hooks/useSEO';
import { supabase } from '../lib/supabaseClient';
import type { Category } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface BlogProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de blog com lista de posts e filtros
 */
export const Blog: React.FC<BlogProps> = ({ className }) => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);

  const postsPerPage = 6;

  // SEO para página de blog
  const selectedCategoryData = categories.find(
    (cat) => cat.id === selectedCategory
  );
  const seoData = useSEO({
    pageType: 'blog',
    customTitle: searchQuery
      ? `Busca: "${searchQuery}" | Blog Blueprint`
      : selectedCategoryData
      ? `${selectedCategoryData.name} | Blog Blueprint`
      : 'Blog | Blueprint - Artigos sobre Tecnologia',
    customDescription: searchQuery
      ? `Resultados da busca por "${searchQuery}" no Blueprint Blog. Artigos sobre tecnologia, desenvolvimento e inovação.`
      : selectedCategoryData
      ? `Artigos sobre ${selectedCategoryData.name.toLowerCase()} no Blueprint Blog. ${
          selectedCategoryData.description ||
          'Conteúdo especializado e atualizado.'
        }`
      : 'Explore todos os artigos do Blueprint Blog sobre tecnologia, programação, design e inovação digital.',
    customKeywords: [
      'blog',
      'artigos',
      'tecnologia',
      'programação',
      ...(searchQuery ? [searchQuery] : []),
      ...(selectedCategoryData
        ? [selectedCategoryData.name.toLowerCase()]
        : []),
    ],
  });

  // Usar hook real do Supabase para posts
  const { posts, loading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
  });

  // Carrega categorias do Supabase
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('categories')
          .select('*')
          .order('name');

        if (categoriesError) {
          console.error('Erro ao carregar categorias:', categoriesError);
          return;
        }

        // Calcular postsCount para cada categoria
        const categoriesWithCount = (categoriesData || []).map((category) => ({
          ...category,
          postsCount: posts.filter((post) => post.category.id === category.id)
            .length,
        }));

        setCategories(categoriesWithCount);
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      }
    };

    if (posts.length > 0) {
      loadCategories();
    }
  }, [posts]);

  // Filtra posts baseado na busca e categoria
  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.author.name.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      selectedCategory === 'all' || post.category.id === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Paginação
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(
    startIndex,
    startIndex + postsPerPage
  );

  // Reset página quando filtros mudam
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory]);

  // Função para navegar para o post
  const handlePostClick = (post: any) => {
    navigate(`/post/${post.slug}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-neon-cyan border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-cyber-muted">Carregando posts...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="blog"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Blog', url: '/blog' },
        ]}
      />

      <div className={clsx('space-y-8', className)}>
        {/* Header */}
        <header className="text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-cyber-text mb-4">
            📝 Blog
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Artigos sobre tecnologia, desenvolvimento e inovação digital
          </p>
        </header>

        {/* Filters */}
        <section className="bg-cyber-surface rounded-lg p-6 border border-cyber-border">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="flex-1 w-full lg:w-auto">
              <Input
                type="search"
                placeholder="Buscar posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                }
                fullWidth
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory('all')}
                className={clsx(
                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                  selectedCategory === 'all'
                    ? 'bg-neon-cyan text-cyber-bg'
                    : 'bg-cyber-bg text-cyber-muted hover:text-cyber-text border border-cyber-border'
                )}>
                Todas
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={clsx(
                    'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2',
                    selectedCategory === category.id
                      ? 'text-cyber-bg border'
                      : 'bg-cyber-bg text-cyber-muted hover:text-cyber-text border border-cyber-border'
                  )}
                  style={{
                    backgroundColor:
                      selectedCategory === category.id
                        ? category.color
                        : undefined,
                    borderColor:
                      selectedCategory === category.id
                        ? category.color
                        : undefined,
                  }}>
                  <div
                    className="w-2 h-2 rounded-full mr-1"
                    style={{ backgroundColor: category.color }}
                  />
                  <span>{category.name}</span>
                  <span className="text-xs opacity-75">
                    ({category.postsCount})
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Results Info */}
          <div className="mt-4 text-sm text-cyber-muted">
            {filteredPosts.length === 0
              ? 'Nenhum post encontrado'
              : `${filteredPosts.length} post${
                  filteredPosts.length !== 1 ? 's' : ''
                } encontrado${filteredPosts.length !== 1 ? 's' : ''}`}
          </div>
        </section>

        {/* Posts Grid */}
        <section>
          <PostGrid
            posts={paginatedPosts}
            loading={loading}
            layout="grid"
            columns={3}
            showAuthor={true}
            showDate={true}
            showExcerpt={true}
            showViews={true}
            showReadTime={true}
            showInteractions={true}
            onPostClick={handlePostClick}
            emptyMessage="Nenhum post encontrado"
            emptyIcon="🔍"
          />
        </section>

        {/* Pagination */}
        {totalPages > 1 && (
          <section className="flex justify-center">
            <div className="flex items-center space-x-2">
              <Button
                variant="secondary"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}>
                ← Anterior
              </Button>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={clsx(
                      'w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200',
                      currentPage === page
                        ? 'bg-neon-cyan text-cyber-bg'
                        : 'bg-cyber-surface text-cyber-muted hover:text-cyber-text border border-cyber-border'
                    )}>
                    {page}
                  </button>
                )
              )}

              <Button
                variant="secondary"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}>
                Próxima →
              </Button>
            </div>
          </section>
        )}
      </div>
    </>
  );
};

export default Blog;
