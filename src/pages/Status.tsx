/**
 * @fileoverview Página de Status do Sistema do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Activity, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface StatusPageProps extends BaseComponentProps {}

interface ServiceStatus {
  name: string;
  status: 'operational' | 'degraded' | 'outage';
  uptime: string;
  responseTime: string;
}

// ============================================================================
// DADOS MOCKADOS
// ============================================================================

const services: ServiceStatus[] = [
  {
    name: 'Website Principal',
    status: 'operational',
    uptime: '99.9%',
    responseTime: '120ms'
  },
  {
    name: 'API v2',
    status: 'operational',
    uptime: '99.8%',
    responseTime: '85ms'
  },
  {
    name: 'Base de Dados',
    status: 'operational',
    uptime: '99.9%',
    responseTime: '45ms'
  },
  {
    name: 'CDN',
    status: 'operational',
    uptime: '99.7%',
    responseTime: '25ms'
  }
];

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de status do sistema
 */
export const Status: React.FC<StatusPageProps> = ({ className }) => {
  const statusClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  const getStatusIcon = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'operational':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'degraded':
        return <AlertCircle className="w-5 h-5 text-yellow-400" />;
      case 'outage':
        return <AlertCircle className="w-5 h-5 text-red-400" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'operational':
        return 'Operacional';
      case 'degraded':
        return 'Degradado';
      case 'outage':
        return 'Indisponível';
      default:
        return 'Desconhecido';
    }
  };

  const getStatusColor = (status: ServiceStatus['status']) => {
    switch (status) {
      case 'operational':
        return 'text-green-400';
      case 'degraded':
        return 'text-yellow-400';
      case 'outage':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className={statusClasses}>
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Activity className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Status do Sistema
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Monitoramento em tempo real dos nossos serviços
          </p>
        </div>

        {/* Status Geral */}
        <Card className="mb-8 border-green-500/30 bg-green-500/5">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-3">
              <CheckCircle className="w-8 h-8 text-green-400" />
              <div className="text-center">
                <h2 className="text-2xl font-bold text-green-400 mb-1">
                  Todos os Sistemas Operacionais
                </h2>
                <p className="text-cyber-muted">
                  Última atualização: {new Date().toLocaleString('pt-BR')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status de Desenvolvimento */}
        <Card className="mb-8 border-amber-500/30 bg-amber-500/5">
          <CardHeader 
            title="🚧 Em Desenvolvimento" 
            subtitle="Esta página está sendo construída"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="text-cyber-muted">
                <p className="mb-2">Recursos de monitoramento planejados:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Monitoramento em tempo real</li>
                  <li>Histórico de incidentes</li>
                  <li>Métricas de performance</li>
                  <li>Notificações de status</li>
                  <li>SLA e uptime detalhados</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Serviços */}
        <Card className="mb-8">
          <CardHeader title="🔧 Status dos Serviços" />
          <CardContent>
            <div className="space-y-4">
              {services.map((service, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(service.status)}
                    <div>
                      <h3 className="font-semibold text-cyber-text">
                        {service.name}
                      </h3>
                      <p className={clsx('text-sm', getStatusColor(service.status))}>
                        {getStatusText(service.status)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="text-sm text-cyber-muted">
                      Uptime: <span className="text-green-400">{service.uptime}</span>
                    </div>
                    <div className="text-sm text-cyber-muted">
                      Resposta: <span className="text-neon-cyan">{service.responseTime}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Métricas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">99.9%</div>
              <div className="text-cyber-muted">Uptime Geral</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-neon-cyan mb-2">68ms</div>
              <div className="text-cyber-muted">Tempo de Resposta</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-neon-purple mb-2">0</div>
              <div className="text-cyber-muted">Incidentes Ativos</div>
            </CardContent>
          </Card>
        </div>

        {/* Histórico */}
        <Card>
          <CardHeader title="📊 Histórico Recente" />
          <CardContent>
            <div className="space-y-3">
              {[
                { date: '2025-01-25', status: 'operational', message: 'Todos os sistemas operacionais' },
                { date: '2025-01-24', status: 'operational', message: 'Manutenção programada concluída' },
                { date: '2025-01-23', status: 'operational', message: 'Atualização de segurança aplicada' }
              ].map((incident, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 rounded-lg bg-cyber-surface">
                  {getStatusIcon(incident.status as ServiceStatus['status'])}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="text-cyber-text">{incident.message}</span>
                      <span className="text-cyber-muted text-sm">{incident.date}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Status;
