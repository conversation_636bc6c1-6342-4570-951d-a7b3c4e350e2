/**
 * @fileoverview Página de Suporte do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { HelpCircle, Mail, MessageCircle, Phone } from 'lucide-react';
import { Card, CardContent, CardHeader } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface SupportPageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de suporte e ajuda
 */
export const Support: React.FC<SupportPageProps> = ({ className }) => {
  const supportClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  const faqItems = [
    {
      question: 'Como posso contribuir com o projeto?',
      answer: 'Você pode contribuir através do GitHub, reportando bugs, sugerindo features ou enviando pull requests.'
    },
    {
      question: 'Como funciona a autenticação?',
      answer: 'Utilizamos JWT tokens para autenticação segura. Veja nossa documentação da API para mais detalhes.'
    },
    {
      question: 'Posso usar a API comercialmente?',
      answer: 'Sim, nossa API está disponível para uso comercial. Consulte nossos termos de uso para mais informações.'
    }
  ];

  return (
    <div className={supportClasses}>
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <HelpCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Suporte
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Estamos aqui para ajudar você com qualquer dúvida ou problema
          </p>
        </div>

        {/* Status de Desenvolvimento */}
        <Card className="mb-8 border-amber-500/30 bg-amber-500/5">
          <CardHeader 
            title="🚧 Em Desenvolvimento" 
            subtitle="Esta página está sendo construída"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="text-cyber-muted">
                <p className="mb-2">Recursos de suporte planejados:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Sistema de tickets de suporte</li>
                  <li>Chat ao vivo</li>
                  <li>Base de conhecimento expandida</li>
                  <li>Tutoriais em vídeo</li>
                  <li>Fórum da comunidade</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Canais de Suporte */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="hover:border-neon-cyan/50 transition-colors">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-neon-cyan/20 flex items-center justify-center">
                  <Mail className="w-6 h-6 text-neon-cyan" />
                </div>
                <h3 className="text-xl font-bold text-cyber-text mb-2">Email</h3>
                <p className="text-cyber-muted mb-4">
                  Envie suas dúvidas por email e responderemos em até 24h
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center px-4 py-2 bg-neon-cyan/20 text-neon-cyan rounded-lg hover:bg-neon-cyan/30 transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:border-neon-purple/50 transition-colors">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-neon-purple/20 flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-neon-purple" />
                </div>
                <h3 className="text-xl font-bold text-cyber-text mb-2">GitHub</h3>
                <p className="text-cyber-muted mb-4">
                  Reporte bugs ou solicite features no nosso repositório
                </p>
                <a
                  href="https://github.com/blueprintblog/v2/issues"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-neon-purple/20 text-neon-purple rounded-lg hover:bg-neon-purple/30 transition-colors"
                >
                  Abrir Issue
                </a>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FAQ */}
        <Card className="mb-8">
          <CardHeader title="❓ Perguntas Frequentes" />
          <CardContent>
            <div className="space-y-4">
              {faqItems.map((item, index) => (
                <div key={index} className="border-b border-cyber-border last:border-b-0 pb-4 last:pb-0">
                  <h3 className="font-semibold text-cyber-text mb-2">
                    {item.question}
                  </h3>
                  <p className="text-cyber-muted text-sm">
                    {item.answer}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recursos Adicionais */}
        <Card>
          <CardHeader title="📚 Recursos Adicionais" />
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <a
                href="/docs"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors text-center"
              >
                <HelpCircle className="w-8 h-8 text-neon-cyan mx-auto mb-2" />
                <h3 className="font-semibold text-cyber-text mb-1">Documentação</h3>
                <p className="text-cyber-muted text-sm">Guias e tutoriais completos</p>
              </a>

              <a
                href="/api"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors text-center"
              >
                <MessageCircle className="w-8 h-8 text-neon-purple mx-auto mb-2" />
                <h3 className="font-semibold text-cyber-text mb-1">API Docs</h3>
                <p className="text-cyber-muted text-sm">Referência da API</p>
              </a>

              <a
                href="/contact"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors text-center"
              >
                <Phone className="w-8 h-8 text-neon-cyan mx-auto mb-2" />
                <h3 className="font-semibold text-cyber-text mb-1">Contato</h3>
                <p className="text-cyber-muted text-sm">Fale diretamente conosco</p>
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Support;
