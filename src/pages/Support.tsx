/**
 * @fileoverview Página de Suporte do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { HelpCircle, Mail, MessageCircle, Phone, ChevronDown, ChevronUp, Search } from 'lucide-react';
import { useState } from 'react';
import { Card, CardContent, CardHeader, Input } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface SupportPageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de suporte e ajuda
 */
export const Support: React.FC<SupportPageProps> = ({ className }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const supportClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  const faqItems = [
    {
      id: 1,
      category: 'Geral',
      question: 'Como posso contribuir com o projeto?',
      answer: 'Você pode contribuir de várias formas: reportando bugs no GitHub, sugerindo novas features, enviando pull requests, melhorando a documentação ou compartilhando o projeto. Consulte nosso guia de contribuição para mais detalhes.',
      tags: ['contribuição', 'github', 'desenvolvimento']
    },
    {
      id: 2,
      category: 'Técnico',
      question: 'Como funciona a autenticação?',
      answer: 'Utilizamos o sistema de autenticação do Supabase com JWT tokens. O processo inclui login via email/senha, autenticação social (Google, GitHub) e tokens de sessão seguros. Veja nossa documentação da API para implementação detalhada.',
      tags: ['autenticação', 'jwt', 'supabase', 'segurança']
    },
    {
      id: 3,
      category: 'Legal',
      question: 'Posso usar a API comercialmente?',
      answer: 'Sim, nossa API está disponível para uso comercial sob a licença MIT. Consulte nossos termos de uso e política de privacidade para mais informações sobre limitações e responsabilidades.',
      tags: ['api', 'comercial', 'licença', 'termos']
    },
    {
      id: 4,
      category: 'Técnico',
      question: 'Como configurar o ambiente de desenvolvimento?',
      answer: 'Você precisa do Node.js 18+, uma conta no Supabase e Git. Clone o repositório, instale as dependências com npm install, configure as variáveis de ambiente e execute npm run dev. Consulte nossa documentação para detalhes.',
      tags: ['desenvolvimento', 'setup', 'nodejs', 'supabase']
    },
    {
      id: 5,
      category: 'Funcionalidades',
      question: 'Como criar e publicar posts?',
      answer: 'Faça login como administrador, acesse o painel de controle, clique em "Novo Post", preencha o título, conteúdo, categoria e tags. Use o editor Markdown para formatação. Salve como rascunho ou publique diretamente.',
      tags: ['posts', 'publicação', 'markdown', 'admin']
    },
    {
      id: 6,
      category: 'Performance',
      question: 'Como otimizar a performance do blog?',
      answer: 'O blog já inclui otimizações como lazy loading, cache de imagens, minificação de assets e PWA. Para melhor performance, use CDN para imagens, configure cache no servidor e monitore métricas com ferramentas como Lighthouse.',
      tags: ['performance', 'otimização', 'pwa', 'cache']
    }
  ];

  // Filtrar FAQs baseado na busca
  const filteredFaqs = faqItems.filter(item => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      item.question.toLowerCase().includes(query) ||
      item.answer.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query) ||
      item.tags.some(tag => tag.toLowerCase().includes(query))
    );
  });

  const toggleFaq = (id: number) => {
    setExpandedFaq(expandedFaq === id ? null : id);
  };

  return (
    <div className={supportClasses}>
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <HelpCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Suporte
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Estamos aqui para ajudar você com qualquer dúvida ou problema
          </p>
        </div>

        {/* Status de Desenvolvimento */}
        <Card className="mb-8 border-amber-500/30 bg-amber-500/5">
          <CardHeader 
            title="🚧 Em Desenvolvimento" 
            subtitle="Esta página está sendo construída"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="text-cyber-muted">
                <p className="mb-2">Recursos de suporte planejados:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Sistema de tickets de suporte</li>
                  <li>Chat ao vivo</li>
                  <li>Base de conhecimento expandida</li>
                  <li>Tutoriais em vídeo</li>
                  <li>Fórum da comunidade</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Canais de Suporte */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="hover:border-neon-cyan/50 transition-colors">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-neon-cyan/20 flex items-center justify-center">
                  <Mail className="w-6 h-6 text-neon-cyan" />
                </div>
                <h3 className="text-xl font-bold text-cyber-text mb-2">Email</h3>
                <p className="text-cyber-muted mb-4">
                  Envie suas dúvidas por email e responderemos em até 24h
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center px-4 py-2 bg-neon-cyan/20 text-neon-cyan rounded-lg hover:bg-neon-cyan/30 transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:border-neon-purple/50 transition-colors">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-neon-purple/20 flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-neon-purple" />
                </div>
                <h3 className="text-xl font-bold text-cyber-text mb-2">GitHub</h3>
                <p className="text-cyber-muted mb-4">
                  Reporte bugs ou solicite features no nosso repositório
                </p>
                <a
                  href="https://github.com/blueprintblog/v2/issues"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-neon-purple/20 text-neon-purple rounded-lg hover:bg-neon-purple/30 transition-colors"
                >
                  Abrir Issue
                </a>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* FAQ */}
        <Card className="mb-8">
          <CardHeader title="❓ Perguntas Frequentes" />
          <CardContent>
            {/* Busca no FAQ */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cyber-muted" />
                <Input
                  type="text"
                  placeholder="Buscar nas perguntas frequentes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              {searchQuery && (
                <p className="text-cyber-muted text-sm mt-2">
                  {filteredFaqs.length} resultado(s) encontrado(s)
                </p>
              )}
            </div>

            {/* Lista de FAQs */}
            <div className="space-y-3">
              {filteredFaqs.length > 0 ? filteredFaqs.map((item) => (
                <div key={item.id} className="border border-cyber-border rounded-lg overflow-hidden">
                  <button
                    onClick={() => toggleFaq(item.id)}
                    className="w-full p-4 text-left hover:bg-cyber-surface/50 transition-colors flex items-center justify-between"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="px-2 py-1 rounded text-xs bg-neon-cyan/20 text-neon-cyan">
                          {item.category}
                        </span>
                      </div>
                      <h3 className="font-semibold text-cyber-text">
                        {item.question}
                      </h3>
                    </div>
                    <div className="ml-4">
                      {expandedFaq === item.id ? (
                        <ChevronUp className="w-5 h-5 text-cyber-muted" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-cyber-muted" />
                      )}
                    </div>
                  </button>

                  {expandedFaq === item.id && (
                    <div className="px-4 pb-4 border-t border-cyber-border bg-cyber-surface/30">
                      <p className="text-cyber-muted text-sm mb-3 pt-3">
                        {item.answer}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {item.tags.map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className="px-2 py-1 rounded text-xs bg-cyber-border/50 text-cyber-muted"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )) : (
                <div className="text-center py-8">
                  <HelpCircle className="w-12 h-12 text-cyber-muted mx-auto mb-4" />
                  <p className="text-cyber-muted">
                    Nenhuma pergunta encontrada para "{searchQuery}"
                  </p>
                  <p className="text-cyber-muted text-sm mt-2">
                    Tente usar termos diferentes ou entre em contato conosco.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recursos Adicionais */}
        <Card>
          <CardHeader title="📚 Recursos Adicionais" />
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <a
                href="/docs"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors text-center"
              >
                <HelpCircle className="w-8 h-8 text-neon-cyan mx-auto mb-2" />
                <h3 className="font-semibold text-cyber-text mb-1">Documentação</h3>
                <p className="text-cyber-muted text-sm">Guias e tutoriais completos</p>
              </a>

              <a
                href="/api"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors text-center"
              >
                <MessageCircle className="w-8 h-8 text-neon-purple mx-auto mb-2" />
                <h3 className="font-semibold text-cyber-text mb-1">API Docs</h3>
                <p className="text-cyber-muted text-sm">Referência da API</p>
              </a>

              <a
                href="/contact"
                className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors text-center"
              >
                <Phone className="w-8 h-8 text-neon-cyan mx-auto mb-2" />
                <h3 className="font-semibold text-cyber-text mb-1">Contato</h3>
                <p className="text-cyber-muted text-sm">Fale diretamente conosco</p>
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Support;
