/**
 * @fileoverview Página de Arquivo do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Calendar, Clock, FileText } from 'lucide-react';
import { useState } from 'react';
import { Card, CardContent, CardHeader } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ArchivePageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de arquivo com posts organizados por data
 */
export const Archive: React.FC<ArchivePageProps> = ({ className }) => {
  const [selectedYear, setSelectedYear] = useState<number>(2025);

  const archiveClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  return (
    <div className={archiveClasses}>
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <FileText className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Arquivo
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Explore todos os posts organizados por data de publicação
          </p>
        </div>

        {/* Status de Desenvolvimento */}
        <Card className="mb-8 border-amber-500/30 bg-amber-500/5">
          <CardHeader 
            title="🚧 Em Desenvolvimento" 
            subtitle="Esta página está sendo construída"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 text-amber-400">
                <Clock className="w-5 h-5" />
                <span className="font-medium">Status: Em construção</span>
              </div>
              <div className="text-cyber-muted">
                <p className="mb-2">Funcionalidades planejadas:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Organização de posts por ano e mês</li>
                  <li>Filtros por categoria e tag</li>
                  <li>Busca no arquivo histórico</li>
                  <li>Estatísticas de publicação</li>
                  <li>Timeline interativa</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preview da Interface */}
        <div className="grid gap-6">
          {/* Seletor de Ano */}
          <Card>
            <CardHeader title="📅 Selecionar Ano" />
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {[2025, 2024, 2023].map((year) => (
                  <button
                    key={year}
                    onClick={() => setSelectedYear(year)}
                    className={clsx(
                      'px-4 py-2 rounded-lg border transition-all duration-200',
                      selectedYear === year
                        ? 'bg-neon-cyan/20 border-neon-cyan text-neon-cyan'
                        : 'border-cyber-border text-cyber-muted hover:border-neon-cyan/50'
                    )}
                  >
                    {year}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Preview dos Meses */}
          <Card>
            <CardHeader title={`📊 Posts de ${selectedYear}`} />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  'Janeiro', 'Fevereiro', 'Março', 'Abril', 
                  'Maio', 'Junho', 'Julho', 'Agosto',
                  'Setembro', 'Outubro', 'Novembro', 'Dezembro'
                ].map((month) => (
                  <div
                    key={month}
                    className="p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors cursor-pointer"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-neon-cyan" />
                        <span className="font-medium">{month}</span>
                      </div>
                      <span className="text-sm text-cyber-muted">
                        {Math.floor(Math.random() * 10)} posts
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Ações Temporárias */}
          <Card>
            <CardHeader title="🔗 Navegação Alternativa" />
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <a
                  href="/blog"
                  className="inline-flex items-center px-4 py-2 bg-neon-cyan/20 text-neon-cyan rounded-lg hover:bg-neon-cyan/30 transition-colors"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Ver Todos os Posts
                </a>
                <a
                  href="/"
                  className="inline-flex items-center px-4 py-2 border border-cyber-border text-cyber-text rounded-lg hover:border-neon-cyan/50 transition-colors"
                >
                  Voltar ao Início
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Archive;
