/**
 * @fileoverview Página de Arquivo do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Calendar, Clock, FileText, Filter, Search } from 'lucide-react';
import { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, Input } from '../components/ui';
import { usePosts } from '../hooks/usePosts';
import { useCategories } from '../hooks/useCategories';
import { useAnalytics } from '../hooks/useAnalytics';
import type { BaseComponentProps, Post } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ArchivePageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de arquivo com posts organizados por data
 */
export const Archive: React.FC<ArchivePageProps> = ({ className }) => {
  const { trackEvent, trackSearch } = useAnalytics();

  const [selectedYear, setSelectedYear] = useState<number>(2025);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Track page view
  useEffect(() => {
    trackEvent('page_view', {
      page_title: 'Archive',
      page_location: '/archive'
    });
  }, [trackEvent]);

  // Track search
  useEffect(() => {
    if (searchQuery.length > 2) {
      const timeoutId = setTimeout(() => {
        trackSearch(searchQuery);
      }, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [searchQuery, trackSearch]);

  // Hooks para dados reais
  const { posts: allPosts, loading } = usePosts({
    status: 'published',
    orderBy: 'published_at',
    orderDirection: 'desc',
  });
  const { categories } = useCategories();

  // Filtrar e organizar posts
  const filteredPosts = useMemo(() => {
    return allPosts.filter((post: Post) => {
      const postYear = new Date(post.publishedAt).getFullYear();
      const matchesYear = postYear === selectedYear;
      const matchesCategory = selectedCategory === 'all' || post.category.slug === selectedCategory;
      const matchesSearch = searchQuery === '' ||
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesYear && matchesCategory && matchesSearch;
    });
  }, [allPosts, selectedYear, selectedCategory, searchQuery]);

  // Organizar posts por mês
  const postsByMonth = useMemo(() => {
    const months = Array.from({ length: 12 }, (_, i) => ({
      name: new Date(2025, i).toLocaleDateString('pt-BR', { month: 'long' }),
      number: i + 1,
      posts: filteredPosts.filter((post: Post) => {
        const postMonth = new Date(post.publishedAt).getMonth() + 1;
        return postMonth === i + 1;
      })
    }));
    return months;
  }, [filteredPosts]);

  // Anos disponíveis
  const availableYears = useMemo(() => {
    const years = [...new Set(allPosts.map((post: Post) => new Date(post.publishedAt).getFullYear()))];
    return years.sort((a, b) => b - a);
  }, [allPosts]);

  const archiveClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  return (
    <div className={archiveClasses}>
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <FileText className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Arquivo
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Explore todos os posts organizados por data de publicação
          </p>
        </div>

        {/* Filtros e Busca */}
        <Card className="mb-8">
          <CardHeader title="🔍 Filtros" />
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Busca */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-cyber-muted" />
                <Input
                  type="text"
                  placeholder="Buscar posts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filtro por Categoria */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 rounded-lg border border-cyber-border bg-cyber-surface text-cyber-text focus:border-neon-cyan focus:outline-none"
              >
                <option value="all">Todas as Categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.slug}>
                    {category.name}
                  </option>
                ))}
              </select>

              {/* Estatísticas */}
              <div className="flex items-center space-x-2 text-cyber-muted">
                <Filter className="w-4 h-4" />
                <span className="text-sm">
                  {filteredPosts.length} posts encontrados
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preview da Interface */}
        <div className="grid gap-6">
          {/* Seletor de Ano */}
          <Card>
            <CardHeader title="📅 Selecionar Ano" />
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {availableYears.length > 0 ? availableYears.map((year) => (
                  <button
                    key={year}
                    onClick={() => setSelectedYear(year)}
                    className={clsx(
                      'px-4 py-2 rounded-lg border transition-all duration-200',
                      selectedYear === year
                        ? 'bg-neon-cyan/20 border-neon-cyan text-neon-cyan'
                        : 'border-cyber-border text-cyber-muted hover:border-neon-cyan/50'
                    )}
                  >
                    {year}
                  </button>
                )) : (
                  <p className="text-cyber-muted">Carregando anos...</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Posts por Mês */}
          <Card>
            <CardHeader title={`📊 Posts de ${selectedYear}`} />
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <Clock className="w-8 h-8 text-neon-cyan mx-auto mb-4 animate-spin" />
                  <p className="text-cyber-muted">Carregando posts...</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {postsByMonth.map((month) => (
                    <div
                      key={month.number}
                      className={clsx(
                        'p-4 rounded-lg border transition-colors cursor-pointer',
                        month.posts.length > 0
                          ? 'border-cyber-border hover:border-neon-cyan/50'
                          : 'border-cyber-border/50 opacity-50'
                      )}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-neon-cyan" />
                          <span className="font-medium capitalize">{month.name}</span>
                        </div>
                        <span className={clsx(
                          'text-sm',
                          month.posts.length > 0 ? 'text-neon-cyan' : 'text-cyber-muted'
                        )}>
                          {month.posts.length} posts
                        </span>
                      </div>

                      {/* Lista de posts do mês */}
                      {month.posts.length > 0 && (
                        <div className="mt-3 space-y-1">
                          {month.posts.slice(0, 3).map((post: Post) => (
                            <a
                              key={post.id}
                              href={`/post/${post.slug}`}
                              className="block text-xs text-cyber-muted hover:text-neon-cyan transition-colors truncate"
                            >
                              • {post.title}
                            </a>
                          ))}
                          {month.posts.length > 3 && (
                            <p className="text-xs text-cyber-muted">
                              +{month.posts.length - 3} mais...
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Ações Temporárias */}
          <Card>
            <CardHeader title="🔗 Navegação Alternativa" />
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <a
                  href="/blog"
                  className="inline-flex items-center px-4 py-2 bg-neon-cyan/20 text-neon-cyan rounded-lg hover:bg-neon-cyan/30 transition-colors"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Ver Todos os Posts
                </a>
                <a
                  href="/"
                  className="inline-flex items-center px-4 py-2 border border-cyber-border text-cyber-text rounded-lg hover:border-neon-cyan/50 transition-colors"
                >
                  Voltar ao Início
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Archive;
