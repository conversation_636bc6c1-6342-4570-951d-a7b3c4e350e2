/**
 * @fileoverview Página de Documentação da API do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Code, Database, Key, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ApiDocsPageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de documentação da API
 */
export const ApiDocs: React.FC<ApiDocsPageProps> = ({ className }) => {
  const apiDocsClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  return (
    <div className={apiDocsClasses}>
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Code className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            API Documentation
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Documentação completa da API RESTful do Blueprint Blog v2
          </p>
        </div>

        {/* Status de Desenvolvimento */}
        <Card className="mb-8 border-amber-500/30 bg-amber-500/5">
          <CardHeader 
            title="🚧 Em Desenvolvimento" 
            subtitle="Esta página está sendo construída"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="text-cyber-muted">
                <p className="mb-2">Documentação da API planejada:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Endpoints completos com exemplos</li>
                  <li>Esquemas de autenticação</li>
                  <li>Rate limiting e quotas</li>
                  <li>Códigos de erro e troubleshooting</li>
                  <li>SDKs e bibliotecas cliente</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Overview */}
        <div className="grid gap-6 mb-8">
          <Card>
            <CardHeader title="🔌 Visão Geral da API" />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Database className="w-6 h-6 text-neon-cyan flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-cyber-text mb-1">RESTful API</h3>
                      <p className="text-cyber-muted text-sm">
                        API seguindo padrões REST com endpoints intuitivos
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Key className="w-6 h-6 text-neon-purple flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-cyber-text mb-1">Autenticação JWT</h3>
                      <p className="text-cyber-muted text-sm">
                        Sistema seguro de autenticação baseado em tokens
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Zap className="w-6 h-6 text-neon-cyan flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-cyber-text mb-1">Rate Limiting</h3>
                      <p className="text-cyber-muted text-sm">
                        Controle de taxa para garantir performance
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Code className="w-6 h-6 text-neon-purple flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-cyber-text mb-1">JSON Response</h3>
                      <p className="text-cyber-muted text-sm">
                        Respostas estruturadas em formato JSON
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Base URL */}
          <Card>
            <CardHeader title="🌐 Base URL" />
            <CardContent>
              <div className="bg-cyber-surface rounded-lg p-4 border border-cyber-border">
                <code className="text-neon-cyan">
                  https://api.blueprintblog.tech/v2
                </code>
              </div>
            </CardContent>
          </Card>

          {/* Endpoints Preview */}
          <Card>
            <CardHeader title="📋 Principais Endpoints" />
            <CardContent>
              <div className="space-y-4">
                {[
                  { method: 'GET', path: '/posts', desc: 'Listar todos os posts' },
                  { method: 'GET', path: '/posts/{id}', desc: 'Obter post específico' },
                  { method: 'POST', path: '/posts', desc: 'Criar novo post' },
                  { method: 'GET', path: '/categories', desc: 'Listar categorias' },
                  { method: 'GET', path: '/tags', desc: 'Listar tags' },
                  { method: 'POST', path: '/auth/login', desc: 'Autenticar usuário' }
                ].map((endpoint, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <span className={clsx(
                        'px-2 py-1 rounded text-xs font-mono',
                        endpoint.method === 'GET' ? 'bg-green-500/20 text-green-400' : 'bg-blue-500/20 text-blue-400'
                      )}>
                        {endpoint.method}
                      </span>
                      <code className="text-neon-cyan">{endpoint.path}</code>
                    </div>
                    <span className="text-cyber-muted text-sm">{endpoint.desc}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <Card>
          <CardHeader title="🚀 Comece a Usar" />
          <CardContent>
            <div className="text-center">
              <p className="text-cyber-muted mb-6">
                A documentação completa estará disponível em breve. 
                Entre em contato para acesso antecipado.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-neon-cyan to-neon-purple text-white rounded-lg font-medium hover:scale-105 transition-transform"
                >
                  <Code className="w-4 h-4 mr-2" />
                  Solicitar Acesso
                </a>
                <a
                  href="/docs"
                  className="inline-flex items-center px-6 py-3 border border-cyber-border text-cyber-text rounded-lg hover:border-neon-cyan/50 transition-colors"
                >
                  Ver Documentação Geral
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ApiDocs;
