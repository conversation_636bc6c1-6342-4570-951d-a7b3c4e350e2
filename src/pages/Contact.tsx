/**
 * @fileoverview Página de Contato do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Mail, MapPin, Phone, Send, MessageCircle } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Button, Card, CardContent, CardHeader, Input, Textarea } from '../components/ui';
import { useAnalytics } from '../hooks/useAnalytics';
import { usePagePerformance } from '../hooks/usePagePerformance';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ContactPageProps extends BaseComponentProps {}

interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
  type: 'general' | 'support' | 'business' | 'press';
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de contato com formulário funcional
 */
export const Contact: React.FC<ContactPageProps> = ({ className }) => {
  const { trackEvent } = useAnalytics();
  const { markStart, markEnd } = usePagePerformance({ pageName: 'Contact' });

  const [formData, setFormData] = useState<ContactForm>({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Track page view
  useEffect(() => {
    trackEvent('page_view', {
      page_title: 'Contact',
      page_location: '/contact'
    });
  }, [trackEvent]);

  const contactClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  const handleInputChange = (field: keyof ContactForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    // Track form submission attempt
    trackEvent('contact_form_submit_attempt', {
      form_type: formData.type,
      has_subject: formData.subject.length > 0,
      message_length: formData.message.length
    });

    // Marcar início do envio para medir performance
    markStart('form-submission');

    try {
      // Simular envio (integrar com backend real posteriormente)
      await new Promise(resolve => setTimeout(resolve, 2000));

      // TODO: Integrar com serviço de email real
      console.log('Formulário enviado:', formData);

      setSubmitStatus('success');

      // Marcar fim do envio
      markEnd('form-submission');

      // Track successful submission
      trackEvent('contact_form_submit_success', {
        form_type: formData.type,
        message_length: formData.message.length
      });

      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        type: 'general'
      });
    } catch (error) {
      console.error('Erro ao enviar formulário:', error);
      setSubmitStatus('error');

      // Track form error
      trackEvent('contact_form_submit_error', {
        form_type: formData.type,
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactMethods = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: 'Email Geral',
      value: '<EMAIL>',
      description: 'Para dúvidas gerais e parcerias',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: <MessageCircle className="w-6 h-6" />,
      title: 'Suporte Técnico',
      value: '<EMAIL>',
      description: 'Para questões técnicas e bugs',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: 'Imprensa',
      value: '<EMAIL>',
      description: 'Para consultas da mídia',
      href: 'mailto:<EMAIL>'
    }
  ];

  return (
    <div className={contactClasses}>
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Mail className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Entre em Contato
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Estamos aqui para ajudar! Entre em contato conosco para dúvidas, sugestões ou parcerias.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Formulário de Contato */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader title="📝 Envie sua Mensagem" />
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Tipo de Contato */}
                  <div>
                    <label className="block text-cyber-text font-medium mb-3">
                      Tipo de Contato
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {[
                        { value: 'general', label: 'Geral' },
                        { value: 'support', label: 'Suporte' },
                        { value: 'business', label: 'Negócios' },
                        { value: 'press', label: 'Imprensa' }
                      ].map((type) => (
                        <button
                          key={type.value}
                          type="button"
                          onClick={() => handleInputChange('type', type.value as ContactForm['type'])}
                          className={clsx(
                            'px-3 py-2 rounded-lg border text-sm transition-all duration-200',
                            formData.type === type.value
                              ? 'bg-neon-cyan/20 border-neon-cyan text-neon-cyan'
                              : 'border-cyber-border text-cyber-muted hover:border-neon-cyan/50'
                          )}
                        >
                          {type.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Nome e Email */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Nome Completo"
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Seu nome"
                      required
                      fullWidth
                    />
                    <Input
                      label="Email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                      fullWidth
                    />
                  </div>

                  {/* Assunto */}
                  <Input
                    label="Assunto"
                    type="text"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    placeholder="Sobre o que você gostaria de falar?"
                    required
                    fullWidth
                  />

                  {/* Mensagem */}
                  <Textarea
                    label="Mensagem"
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    placeholder="Descreva sua mensagem em detalhes..."
                    rows={6}
                    required
                    fullWidth
                  />

                  {/* Status Messages */}
                  {submitStatus === 'success' && (
                    <div className="p-4 rounded-lg bg-green-500/20 border border-green-500/30">
                      <p className="text-green-400 text-sm">
                        ✅ Mensagem enviada com sucesso! Responderemos em breve.
                      </p>
                    </div>
                  )}

                  {submitStatus === 'error' && (
                    <div className="p-4 rounded-lg bg-red-500/20 border border-red-500/30">
                      <p className="text-red-400 text-sm">
                        ❌ Erro ao enviar mensagem. Tente novamente ou use nosso email direto.
                      </p>
                    </div>
                  )}

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    loading={isSubmitting}
                    disabled={!formData.name || !formData.email || !formData.subject || !formData.message}
                    className="w-full"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    {isSubmitting ? 'Enviando...' : 'Enviar Mensagem'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Informações de Contato */}
          <div className="space-y-6">
            {/* Métodos de Contato */}
            <Card>
              <CardHeader title="📞 Contato Direto" />
              <CardContent>
                <div className="space-y-4">
                  {contactMethods.map((method, index) => (
                    <a
                      key={index}
                      href={method.href}
                      className="block p-4 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors group"
                    >
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 rounded-full bg-neon-cyan/20 flex items-center justify-center text-neon-cyan group-hover:bg-neon-cyan/30 transition-colors">
                          {method.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-cyber-text mb-1">
                            {method.title}
                          </h3>
                          <p className="text-neon-cyan text-sm mb-1">
                            {method.value}
                          </p>
                          <p className="text-cyber-muted text-xs">
                            {method.description}
                          </p>
                        </div>
                      </div>
                    </a>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Localização */}
            <Card>
              <CardHeader title="📍 Localização" />
              <CardContent>
                <div className="flex items-start space-x-3">
                  <MapPin className="w-5 h-5 text-neon-cyan flex-shrink-0 mt-1" />
                  <div>
                    <p className="text-cyber-text font-medium mb-1">
                      Casimiro de Abreu
                    </p>
                    <p className="text-cyber-muted text-sm">
                      Rio de Janeiro, Brasil
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>


          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
