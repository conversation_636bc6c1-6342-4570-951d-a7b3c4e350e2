/**
 * @fileoverview Página de Analytics do painel administrativo
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { BarChart3, Eye, Heart, TrendingUp, Users } from 'lucide-react';
import { motion } from 'motion/react';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader } from '../../components/ui';
import { useBlogStats, usePopularPosts } from '../../hooks/usePostAnalytics';

// ============================================================================
// INTERFACES
// ============================================================================

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: string;
  color: string;
}

interface AnalyticsProps {
  className?: string;
}

// ============================================================================
// COMPONENTES
// ============================================================================

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  trend,
  color,
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="relative">
    <Card
      className={`border-${color}/20 bg-${color}/5 hover:bg-${color}/10 transition-colors`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-cyber-muted">{title}</p>
            <p className={`text-2xl font-bold text-${color}`}>{value}</p>
            {trend && <p className="text-xs text-cyber-muted mt-1">{trend}</p>}
          </div>
          <div className={`p-3 rounded-lg bg-${color}/10`}>{icon}</div>
        </div>
      </CardContent>
    </Card>
  </motion.div>
);

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

/**
 * Página de Analytics com métricas em tempo real
 */
export const Analytics: React.FC<AnalyticsProps> = ({ className }) => {
  const { stats, loading: statsLoading, error: statsError } = useBlogStats();
  const { popularPosts, loading: postsLoading } = usePopularPosts(5);
  const [webVitals, setWebVitals] = useState<any>(null);

  // Carregar Web Vitals
  useEffect(() => {
    import('web-vitals')
      .then((vitals: any) => {
        const { onCLS, onFID, onLCP } = vitals;

        if (onCLS) {
          onCLS((metric: any) => {
            setWebVitals((prev: any) => ({ ...prev, cls: metric.value }));
          });
        }

        if (onFID) {
          onFID((metric: any) => {
            setWebVitals((prev: any) => ({ ...prev, fid: metric.value }));
          });
        }

        if (onLCP) {
          onLCP((metric: any) => {
            setWebVitals((prev: any) => ({ ...prev, lcp: metric.value }));
          });
        }
      })
      .catch(console.error);
  }, []);

  if (statsLoading || postsLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-cyber-surface rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (statsError) {
    return (
      <Card className="border-red-500/20 bg-red-500/5">
        <CardContent className="p-6">
          <p className="text-red-400">
            Erro ao carregar analytics: {statsError}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-8 ${className || ''}`}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-cyber-text mb-2">
          📈 Analytics Dashboard
        </h1>
        <p className="text-cyber-muted">
          Métricas e performance do blog em tempo real
        </p>
      </div>

      {/* Métricas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total de Posts"
          value={stats?.totalPosts || 0}
          icon={<BarChart3 className="h-6 w-6 text-neon-cyan" />}
          color="neon-cyan"
        />

        <MetricCard
          title="Total de Views"
          value={stats?.totalViews?.toLocaleString('pt-BR') || 0}
          icon={<Eye className="h-6 w-6 text-neon-magenta" />}
          color="neon-magenta"
        />

        <MetricCard
          title="Média por Post"
          value={Math.round(stats?.avgViewsPerPost || 0)}
          icon={<TrendingUp className="h-6 w-6 text-neon-yellow" />}
          color="neon-yellow"
        />

        <MetricCard
          title="Post Popular"
          value={stats?.mostPopularPost?.views || 0}
          icon={<Heart className="h-6 w-6 text-neon-green" />}
          trend={stats?.mostPopularPost?.title}
          color="neon-green"
        />
      </div>

      {/* Web Vitals */}
      {webVitals && (
        <Card>
          <CardHeader
            title="⚡ Core Web Vitals"
            subtitle="Performance em tempo real"
          />
          <CardContent className="p-6">
            <div className="mb-4 p-3 bg-neon-cyan/10 border border-neon-cyan/30 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span className="text-sm text-cyber-text font-medium">
                    Vercel Speed Insights Ativo
                  </span>
                </div>
                <a
                  href="https://vercel.com/analytics/speed"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-neon-cyan hover:text-neon-cyan/80 transition-colors">
                  Ver Dashboard Completo →
                </a>
              </div>
              <p className="text-xs text-cyber-muted mt-1">
                Métricas coletadas automaticamente em tempo real
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border border-cyber-border rounded-lg bg-cyber-surface">
                <p className="text-sm text-cyber-muted">LCP</p>
                <p className="text-xl font-bold text-neon-cyan">
                  {webVitals.lcp ? `${Math.round(webVitals.lcp)}ms` : '-'}
                </p>
                <p className="text-xs text-cyber-muted">
                  Largest Contentful Paint
                </p>
              </div>

              <div className="text-center p-4 border border-cyber-border rounded-lg bg-cyber-surface">
                <p className="text-sm text-cyber-muted">FID</p>
                <p className="text-xl font-bold text-neon-magenta">
                  {webVitals.fid ? `${Math.round(webVitals.fid)}ms` : '-'}
                </p>
                <p className="text-xs text-cyber-muted">First Input Delay</p>
              </div>

              <div className="text-center p-4 border border-cyber-border rounded-lg bg-cyber-surface">
                <p className="text-sm text-cyber-muted">CLS</p>
                <p className="text-xl font-bold text-neon-yellow">
                  {webVitals.cls ? webVitals.cls.toFixed(3) : '-'}
                </p>
                <p className="text-xs text-cyber-muted">
                  Cumulative Layout Shift
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Posts Populares */}
      <Card>
        <CardHeader
          title="🔥 Posts Mais Populares"
          subtitle="Top 5 por visualizações"
        />
        <CardContent className="p-6">
          <div className="space-y-3">
            {popularPosts.map((post, index) => (
              <motion.div
                key={post.slug}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 border border-cyber-border rounded-lg hover:bg-cyber-surface/50 transition-colors">
                <div className="flex items-center gap-3">
                  <span className="flex items-center justify-center w-8 h-8 rounded-full bg-neon-cyan/20 text-neon-cyan font-semibold text-sm">
                    {index + 1}
                  </span>
                  <div>
                    <h4 className="font-medium text-cyber-text">
                      {post.title}
                    </h4>
                    <p className="text-sm text-cyber-muted">{post.slug}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-cyber-muted">
                  <Eye className="h-4 w-4" />
                  <span className="font-medium">
                    {post.views.toLocaleString('pt-BR')}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Analytics Links */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-neon-cyan/20 bg-neon-cyan/5">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-neon-cyan mb-2">
              Google Analytics
            </h3>
            <p className="text-sm text-cyber-muted mb-4">
              Métricas detalhadas de tráfego e comportamento dos usuários
            </p>
            <a
              href="https://analytics.google.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 bg-neon-cyan text-cyber-bg rounded-lg hover:bg-neon-cyan/80 transition-colors font-medium">
              <Users className="h-4 w-4" />
              Abrir GA4
            </a>
          </CardContent>
        </Card>

        <Card className="border-neon-magenta/20 bg-neon-magenta/5">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-neon-magenta mb-2">
              Vercel Analytics
            </h3>
            <p className="text-sm text-cyber-muted mb-4">
              Performance e Core Web Vitals em tempo real
            </p>
            <a
              href="https://vercel.com/analytics"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 bg-neon-magenta text-cyber-bg rounded-lg hover:bg-neon-magenta/80 transition-colors font-medium">
              <TrendingUp className="h-4 w-4" />
              Abrir Vercel
            </a>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Analytics;
