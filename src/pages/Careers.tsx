/**
 * @fileoverview Página de Carreiras do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { Briefcase, Clock, MapPin, Users } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader } from '../components/ui';
import type { BaseComponentProps } from '../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface CareersPageProps extends BaseComponentProps {}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de carreiras e oportunidades
 */
export const Careers: React.FC<CareersPageProps> = ({ className }) => {
  const careersClasses = clsx(
    'min-h-screen bg-cyber-bg text-cyber-text',
    'py-12',
    className
  );

  return (
    <div className={careersClasses}>
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-neon-cyan to-neon-purple mb-6">
            <Briefcase className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent mb-4">
            Carreiras
          </h1>
          <p className="text-xl text-cyber-muted max-w-2xl mx-auto">
            Faça parte da nossa jornada de inovação e tecnologia
          </p>
        </div>



        {/* Cultura da Empresa */}
        <Card className="mb-8">
          <CardHeader title="🌟 Nossa Cultura" />
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 rounded-full bg-neon-cyan/20 flex items-center justify-center flex-shrink-0">
                    <Users className="w-4 h-4 text-neon-cyan" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyber-text mb-1">Colaboração</h3>
                    <p className="text-cyber-muted text-sm">
                      Trabalhamos em equipe, valorizando diferentes perspectivas e ideias.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 rounded-full bg-neon-purple/20 flex items-center justify-center flex-shrink-0">
                    <Briefcase className="w-4 h-4 text-neon-purple" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyber-text mb-1">Inovação</h3>
                    <p className="text-cyber-muted text-sm">
                      Buscamos constantemente novas tecnologias e soluções criativas.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 rounded-full bg-neon-cyan/20 flex items-center justify-center flex-shrink-0">
                    <MapPin className="w-4 h-4 text-neon-cyan" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyber-text mb-1">Flexibilidade</h3>
                    <p className="text-cyber-muted text-sm">
                      Oferecemos trabalho remoto e horários flexíveis.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 rounded-full bg-neon-purple/20 flex items-center justify-center flex-shrink-0">
                    <Clock className="w-4 h-4 text-neon-purple" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyber-text mb-1">Crescimento</h3>
                    <p className="text-cyber-muted text-sm">
                      Investimos no desenvolvimento profissional de nossa equipe.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Vagas Futuras */}
        <Card className="mb-8">
          <CardHeader title="💼 Oportunidades Futuras" />
          <CardContent>
            <div className="space-y-4">
              <p className="text-cyber-muted">
                Atualmente não temos vagas abertas, mas estamos sempre em busca de talentos excepcionais. 
                Algumas áreas de interesse incluem:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  'Desenvolvimento Frontend',
                  'Desenvolvimento Backend',
                  'DevOps Engineer',
                  'UI/UX Designer',
                  'Content Creator',
                  'Marketing Digital'
                ].map((area, index) => (
                  <div
                    key={index}
                    className="p-3 rounded-lg border border-cyber-border hover:border-neon-cyan/50 transition-colors"
                  >
                    <span className="text-cyber-text">{area}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <Card>
          <CardHeader title="📧 Interessado?" />
          <CardContent>
            <div className="text-center">
              <p className="text-cyber-muted mb-6">
                Mesmo sem vagas abertas, adoraríamos conhecer você! 
                Envie seu currículo e vamos manter contato.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <a
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-neon-cyan to-neon-purple text-white rounded-lg font-medium hover:scale-105 transition-transform"
                >
                  <Briefcase className="w-4 h-4 mr-2" />
                  Entre em Contato
                </a>
                <a
                  href="/team"
                  className="inline-flex items-center px-6 py-3 border border-cyber-border text-cyber-text rounded-lg hover:border-neon-cyan/50 transition-colors"
                >
                  <Users className="w-4 h-4 mr-2" />
                  Conheça a Equipe
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Careers;
