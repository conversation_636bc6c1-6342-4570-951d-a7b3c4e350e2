/**
 * @fileoverview Página Política de Cookies do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState } from 'react';
import {
  FiSettings,
  FiBarChart,
  FiTarget,
  FiShield,
  FiInfo,
  FiToggleRight,
  FiCircle,
} from 'react-icons/fi';
import { JsonLdSchema, SEOHead } from '../../components/seo';
import useSEO from '../../hooks/useSEO';

// ============================================================================
// INTERFACES
// ============================================================================

interface CookiesProps {
  className?: string;
}

type TabType = 'overview' | 'types' | 'management' | 'third-party' | 'settings';

// interface CookieCategory {
//   id: string;
//   name: string;
//   description: string;
//   essential: boolean;
//   enabled: boolean;
//   cookies: Array<{
//     name: string;
//     purpose: string;
//     duration: string;
//     provider: string;
//   }>;
// }

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Página Política de Cookies - Informações sobre uso de cookies
 */
export const Cookies: React.FC<CookiesProps> = () => {
  // SEO para página Cookies
  const seoData = useSEO({
    pageType: 'page',
    customTitle: 'Política de Cookies | Blueprint Blog',
    customDescription:
      'Política de cookies do Blueprint Blog - Como usamos cookies para melhorar sua experiência e personalizar conteúdo',
    customKeywords: [
      'política de cookies',
      'cookies',
      'rastreamento',
      'analytics',
      'personalização',
      'preferências',
    ],
  });

  const [activeTab, setActiveTab] = useState<TabType>('overview');
  // const [cookieSettings, setCookieSettings] = useState<Record<string, boolean>>({
  //   analytics: true,
  //   marketing: false,
  //   functional: true,
  // });

  const tabs = [
    { id: 'overview' as TabType, label: 'Visão Geral', icon: FiInfo },
    { id: 'types' as TabType, label: 'Tipos de Cookies', icon: FiCircle },
    { id: 'management' as TabType, label: 'Gerenciamento', icon: FiSettings },
    { id: 'third-party' as TabType, label: 'Terceiros', icon: FiTarget },
    { id: 'settings' as TabType, label: 'Configurações', icon: FiToggleRight },
  ];

  // Dados dos cookies serão implementados nas próximas abas
  // const cookieCategories: CookieCategory[] = [...];
  // const toggleCookieSetting = (category: string) => {...};

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="page"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Legal', url: '/legal' },
          { name: 'Política de Cookies', url: '/legal/cookies' },
        ]}
      />

      <div className="min-h-screen bg-cyber-bg py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-neon-cyan/20 rounded-full border border-neon-cyan">
                <FiCircle className="w-12 h-12 text-neon-cyan" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              <span className="text-neon-cyan">Política de</span> Cookies
            </h1>
            <p className="text-cyber-muted text-lg max-w-3xl mx-auto">
              Como usamos cookies para melhorar sua experiência
            </p>
            <div className="h-1 w-32 bg-neon-cyan mx-auto rounded-full mt-6"></div>
            <p className="text-cyber-muted mt-4">
              Última atualização: {new Date().toLocaleDateString('pt-BR')}
            </p>
          </div>

          {/* Navigation Tabs */}
          <div className="flex justify-center mb-12 overflow-x-auto">
            <div className="bg-cyber-surface/50 p-2 rounded-full border border-cyber-surface min-w-max">
              <div className="flex space-x-2">
                {tabs.map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => setActiveTab(id)}
                    className={`flex items-center space-x-2 px-4 py-3 rounded-full transition-all whitespace-nowrap ${
                      activeTab === id
                        ? 'bg-neon-cyan text-cyber-bg font-semibold'
                        : 'text-cyber-muted hover:text-white hover:bg-cyber-surface'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {activeTab === 'overview' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">O que são Cookies?</h2>

                  <div className="space-y-8">
                    <section>
                      <p className="text-cyber-text leading-relaxed mb-6">
                        Cookies são pequenos arquivos de texto armazenados em seu dispositivo
                        quando você visita um site. Eles nos ajudam a fornecer uma melhor
                        experiência, lembrar suas preferências e entender como você usa nosso site.
                      </p>

                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="bg-green-500/10 p-6 rounded-lg border border-green-500/30">
                          <h3 className="text-green-400 font-semibold mb-3 flex items-center">
                            <FiShield className="w-5 h-5 mr-2" />
                            Cookies Essenciais
                          </h3>
                          <p className="text-cyber-text text-sm">
                            Necessários para o funcionamento básico do site.
                            Não podem ser desabilitados.
                          </p>
                        </div>

                        <div className="bg-blue-500/10 p-6 rounded-lg border border-blue-500/30">
                          <h3 className="text-blue-400 font-semibold mb-3 flex items-center">
                            <FiBarChart className="w-5 h-5 mr-2" />
                            Cookies de Analytics
                          </h3>
                          <p className="text-cyber-text text-sm">
                            Nos ajudam a entender como os visitantes interagem
                            com o site para melhorar a experiência.
                          </p>
                        </div>

                        <div className="bg-purple-500/10 p-6 rounded-lg border border-purple-500/30">
                          <h3 className="text-purple-400 font-semibold mb-3 flex items-center">
                            <FiSettings className="w-5 h-5 mr-2" />
                            Cookies Funcionais
                          </h3>
                          <p className="text-cyber-text text-sm">
                            Lembram suas preferências e configurações
                            para personalizar sua experiência.
                          </p>
                        </div>

                        <div className="bg-orange-500/10 p-6 rounded-lg border border-orange-500/30">
                          <h3 className="text-orange-400 font-semibold mb-3 flex items-center">
                            <FiTarget className="w-5 h-5 mr-2" />
                            Cookies de Marketing
                          </h3>
                          <p className="text-cyber-text text-sm">
                            Usados para rastrear visitantes e exibir
                            anúncios relevantes e envolventes.
                          </p>
                        </div>
                      </div>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        Por que Usamos Cookies?
                      </h3>
                      <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                        <ul className="text-cyber-text space-y-3">
                          <li className="flex items-start">
                            <span className="text-neon-cyan mr-3">•</span>
                            <span>Manter você logado durante sua visita</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-neon-cyan mr-3">•</span>
                            <span>Lembrar suas preferências e configurações</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-neon-cyan mr-3">•</span>
                            <span>Entender como você usa nosso site para melhorá-lo</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-neon-cyan mr-3">•</span>
                            <span>Fornecer conteúdo personalizado e relevante</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-neon-cyan mr-3">•</span>
                            <span>Garantir a segurança e prevenir fraudes</span>
                          </li>
                        </ul>
                      </div>
                    </section>
                  </div>
                </div>
              )}

              {/* Placeholder para outras abas */}
              {activeTab !== 'overview' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">
                    {tabs.find(tab => tab.id === activeTab)?.label}
                  </h2>
                  <p className="text-cyber-text">
                    Conteúdo em desenvolvimento...
                  </p>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                <h3 className="text-white font-semibold mb-4 flex items-center">
                  <FiSettings className="w-5 h-5 mr-2 text-neon-cyan" />
                  Gerenciar Cookies
                </h3>
                <p className="text-cyber-text text-sm mb-4">
                  Você pode controlar quais cookies aceitar em seu navegador.
                </p>
                <button
                  onClick={() => setActiveTab('settings')}
                  className="block w-full text-center bg-neon-cyan text-cyber-bg font-semibold py-2 rounded-lg hover:bg-neon-cyan/80 transition-colors text-sm">
                  Configurar Cookies
                </button>
              </div>

              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                <h3 className="text-white font-semibold mb-4">
                  Documentos Relacionados
                </h3>
                <div className="space-y-3">
                  <a
                    href="/legal/privacy"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors text-sm">
                    → Política de Privacidade
                  </a>
                  <a
                    href="/legal/terms"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors text-sm">
                    → Termos de Uso
                  </a>
                  <a
                    href="/legal/lgpd"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors text-sm">
                    → LGPD Compliance
                  </a>
                </div>
              </div>

              <div className="bg-amber-500/10 backdrop-blur-sm rounded-2xl p-6 border border-amber-500/30">
                <h3 className="text-white font-semibold mb-3">
                  Controle Total
                </h3>
                <p className="text-cyber-text text-sm mb-4">
                  Você sempre tem controle sobre seus dados e pode alterar
                  suas preferências a qualquer momento.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="block w-full text-center bg-amber-500 text-cyber-bg font-semibold py-2 rounded-lg hover:bg-amber-600 transition-colors text-sm">
                  Fale Conosco
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Cookies;
