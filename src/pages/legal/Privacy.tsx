/**
 * @fileoverview Página Política de Privacidade do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState } from 'react';
import {
  FiDatabase,
  FiEye,
  FiLock,
  FiMail,
  FiShield,
  FiUser,
  FiUsers,
  FiSettings,
} from 'react-icons/fi';
import { JsonLdSchema, SEOHead } from '../../components/seo';
import useSEO from '../../hooks/useSEO';

// ============================================================================
// INTERFACES
// ============================================================================

interface PrivacyProps {
  className?: string;
}

type TabType = 'collection' | 'usage' | 'sharing' | 'security' | 'rights' | 'contact';

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Página Política de Privacidade - Como coletamos, usamos e protegemos dados
 */
export const Privacy: React.FC<PrivacyProps> = () => {
  // SEO para página Privacy
  const seoData = useSEO({
    pageType: 'page',
    customTitle: 'Política de Privacidade | Blueprint Blog',
    customDescription:
      'Política de privacidade do Blueprint Blog - Como coletamos, usamos e protegemos seus dados pessoais de acordo com a LGPD',
    customKeywords: [
      'política de privacidade',
      'proteção de dados',
      'LGPD',
      'privacidade',
      'dados pessoais',
      'segurança',
    ],
  });

  const [activeTab, setActiveTab] = useState<TabType>('collection');

  const tabs = [
    { id: 'collection' as TabType, label: 'Coleta de Dados', icon: FiDatabase },
    { id: 'usage' as TabType, label: 'Uso dos Dados', icon: FiEye },
    { id: 'sharing' as TabType, label: 'Compartilhamento', icon: FiUsers },
    { id: 'security' as TabType, label: 'Segurança', icon: FiLock },
    { id: 'rights' as TabType, label: 'Seus Direitos', icon: FiUser },
    { id: 'contact' as TabType, label: 'Contato', icon: FiMail },
  ];

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="page"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Legal', url: '/legal' },
          { name: 'Política de Privacidade', url: '/legal/privacy' },
        ]}
      />

      <div className="min-h-screen bg-cyber-bg py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-neon-cyan/20 rounded-full border border-neon-cyan">
                <FiShield className="w-12 h-12 text-neon-cyan" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              <span className="text-neon-cyan">Política de</span> Privacidade
            </h1>
            <p className="text-cyber-muted text-lg max-w-3xl mx-auto">
              Como coletamos, usamos e protegemos seus dados pessoais
            </p>
            <div className="h-1 w-32 bg-neon-cyan mx-auto rounded-full mt-6"></div>
            <p className="text-cyber-muted mt-4">
              Última atualização: {new Date().toLocaleDateString('pt-BR')}
            </p>
          </div>

          {/* Navigation Tabs */}
          <div className="flex justify-center mb-12 overflow-x-auto">
            <div className="bg-cyber-surface/50 p-2 rounded-full border border-cyber-surface min-w-max">
              <div className="flex space-x-2">
                {tabs.map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => setActiveTab(id)}
                    className={`flex items-center space-x-2 px-4 py-3 rounded-full transition-all whitespace-nowrap ${
                      activeTab === id
                        ? 'bg-neon-cyan text-cyber-bg font-semibold'
                        : 'text-cyber-muted hover:text-white hover:bg-cyber-surface'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {activeTab === 'collection' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Coleta de Dados</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        Dados que Coletamos
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-6">
                        Coletamos apenas os dados necessários para fornecer nossos serviços
                        e melhorar sua experiência no Blueprint Blog.
                      </p>

                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-3 flex items-center">
                            <FiUser className="w-5 h-5 mr-2 text-neon-cyan" />
                            Dados Pessoais
                          </h4>
                          <ul className="text-cyber-text text-sm space-y-2">
                            <li>• Nome e sobrenome</li>
                            <li>• Endereço de email</li>
                            <li>• Foto de perfil (opcional)</li>
                            <li>• Preferências de conta</li>
                          </ul>
                        </div>

                        <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-3 flex items-center">
                            <FiSettings className="w-5 h-5 mr-2 text-neon-cyan" />
                            Dados Técnicos
                          </h4>
                          <ul className="text-cyber-text text-sm space-y-2">
                            <li>• Endereço IP</li>
                            <li>• Tipo de navegador</li>
                            <li>• Sistema operacional</li>
                            <li>• Páginas visitadas</li>
                            <li>• Tempo de permanência</li>
                          </ul>
                        </div>
                      </div>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        Como Coletamos
                      </h3>
                      <div className="space-y-4">
                        <div className="bg-blue-500/10 p-4 rounded-lg border border-blue-500/30">
                          <h4 className="text-blue-400 font-semibold mb-2">
                            Diretamente de Você
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Quando você se registra, faz login ou interage com nosso conteúdo.
                          </p>
                        </div>
                        <div className="bg-purple-500/10 p-4 rounded-lg border border-purple-500/30">
                          <h4 className="text-purple-400 font-semibold mb-2">
                            Automaticamente
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Através de cookies, analytics e outras tecnologias de rastreamento.
                          </p>
                        </div>
                        <div className="bg-green-500/10 p-4 rounded-lg border border-green-500/30">
                          <h4 className="text-green-400 font-semibold mb-2">
                            Terceiros
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Provedores de autenticação como Google (com seu consentimento).
                          </p>
                        </div>
                      </div>
                    </section>
                  </div>
                </div>
              )}

              {/* Placeholder para outras abas */}
              {activeTab !== 'collection' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">
                    {tabs.find(tab => tab.id === activeTab)?.label}
                  </h2>
                  <p className="text-cyber-text">
                    Conteúdo em desenvolvimento...
                  </p>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                <h3 className="text-white font-semibold mb-4 flex items-center">
                  <FiShield className="w-5 h-5 mr-2 text-neon-cyan" />
                  Conformidade LGPD
                </h3>
                <p className="text-cyber-text text-sm mb-4">
                  Estamos em total conformidade com a Lei Geral de Proteção de Dados.
                </p>
                <a
                  href="/legal/lgpd"
                  className="block w-full text-center bg-neon-cyan text-cyber-bg font-semibold py-2 rounded-lg hover:bg-neon-cyan/80 transition-colors text-sm">
                  Ver Detalhes LGPD
                </a>
              </div>

              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                <h3 className="text-white font-semibold mb-4">
                  Documentos Relacionados
                </h3>
                <div className="space-y-3">
                  <a
                    href="/legal/terms"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors text-sm">
                    → Termos de Uso
                  </a>
                  <a
                    href="/legal/cookies"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors text-sm">
                    → Política de Cookies
                  </a>
                </div>
              </div>

              <div className="bg-red-500/10 backdrop-blur-sm rounded-2xl p-6 border border-red-500/30">
                <h3 className="text-white font-semibold mb-3">
                  Exercer Seus Direitos
                </h3>
                <p className="text-cyber-text text-sm mb-4">
                  Você tem direito a acessar, corrigir ou excluir seus dados.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="block w-full text-center bg-red-500 text-white font-semibold py-2 rounded-lg hover:bg-red-600 transition-colors text-sm">
                  Solicitar Ação
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Privacy;
