/**
 * @fileoverview Página Termos de Uso do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState } from 'react';
import {
  FiAlertTriangle,
  FiBook,
  FiCheck,
  FiFileText,
  FiGavel,
  FiMail,
  FiShield,
  FiUser,
} from 'react-icons/fi';
import { JsonLdSchema, SEOHead } from '../../components/seo';
import useSEO from '../../hooks/useSEO';

// ============================================================================
// INTERFACES
// ============================================================================

interface TermsProps {
  className?: string;
}

type TabType = 'general' | 'usage' | 'content' | 'liability' | 'rights' | 'final';

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Página Termos de Uso - Condições gerais de uso dos serviços
 */
export const Terms: React.FC<TermsProps> = () => {
  // SEO para página Terms
  const seoData = useSEO({
    pageType: 'page',
    customTitle: 'Termos de Uso | Blueprint Blog',
    customDescription:
      'Termos e condições de uso do Blueprint Blog - Direitos, responsabilidades e regras para utilização dos nossos serviços',
    customKeywords: [
      'termos de uso',
      'condições',
      'direitos',
      'responsabilidades',
      'regras',
      'serviços',
    ],
  });

  const [activeTab, setActiveTab] = useState<TabType>('general');

  const tabs = [
    { id: 'general' as TabType, label: 'Disposições Gerais', icon: FiBook },
    { id: 'usage' as TabType, label: 'Uso do Site', icon: FiUser },
    { id: 'content' as TabType, label: 'Conteúdo', icon: FiShield },
    { id: 'liability' as TabType, label: 'Responsabilidades', icon: FiAlertTriangle },
    { id: 'rights' as TabType, label: 'Direitos Autorais', icon: FiGavel },
    { id: 'final' as TabType, label: 'Disposições Finais', icon: FiCheck },
  ];

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="page"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Legal', url: '/legal' },
          { name: 'Termos de Uso', url: '/legal/terms' },
        ]}
      />

      <div className="min-h-screen bg-cyber-bg py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-neon-cyan/20 rounded-full border border-neon-cyan">
                <FiGavel className="w-12 h-12 text-neon-cyan" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              <span className="text-neon-cyan">Termos de</span> Uso
            </h1>
            <p className="text-cyber-muted text-lg max-w-3xl mx-auto">
              Condições gerais de uso dos serviços do Blueprint Blog
            </p>
            <div className="h-1 w-32 bg-neon-cyan mx-auto rounded-full mt-6"></div>
            <p className="text-cyber-muted mt-4">
              Última atualização: {new Date().toLocaleDateString('pt-BR')}
            </p>
          </div>

          {/* Navigation Tabs */}
          <div className="flex justify-center mb-12 overflow-x-auto">
            <div className="bg-cyber-surface/50 p-2 rounded-full border border-cyber-surface min-w-max">
              <div className="flex space-x-2">
                {tabs.map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => setActiveTab(id)}
                    className={`flex items-center space-x-2 px-4 py-3 rounded-full transition-all whitespace-nowrap ${
                      activeTab === id
                        ? 'bg-neon-cyan text-cyber-bg font-semibold'
                        : 'text-cyber-muted hover:text-white hover:bg-cyber-surface'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {activeTab === 'general' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Disposições Gerais</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        1. Aceitação dos Termos
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Ao acessar e usar o Blueprint Blog, você concorda em cumprir e estar
                        vinculado aos termos e condições de uso descritos neste documento.
                        Se você não concordar com qualquer parte destes termos, não deve
                        usar nosso site.
                      </p>
                      <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                        <h4 className="text-white font-semibold mb-3">
                          Importante:
                        </h4>
                        <ul className="text-cyber-text space-y-2">
                          <li>
                            ✓ Estes termos se aplicam a todos os usuários do site
                          </li>
                          <li>
                            ✓ O uso continuado implica aceitação automática
                          </li>
                          <li>
                            ✓ Reservamo-nos o direito de modificar estes termos
                          </li>
                          <li>
                            ✓ Mudanças entram em vigor imediatamente após publicação
                          </li>
                        </ul>
                      </div>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        2. Definições
                      </h3>
                      <div className="space-y-4">
                        <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-2">
                            "Serviço"
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Refere-se ao site Blueprint Blog e todos os seus recursos,
                            conteúdos e funcionalidades.
                          </p>
                        </div>
                        <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-2">
                            "Usuário"
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Qualquer pessoa que acesse ou use o serviço, seja como
                            visitante ou usuário registrado.
                          </p>
                        </div>
                        <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-2">
                            "Conteúdo"
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Inclui textos, imagens, vídeos, códigos e qualquer material
                            disponibilizado no site.
                          </p>
                        </div>
                      </div>
                    </section>
                  </div>
                </div>
              )}

              {/* Placeholder para outras abas - será implementado via str-replace-editor */}
              {activeTab !== 'general' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">
                    {tabs.find(tab => tab.id === activeTab)?.label}
                  </h2>
                  <p className="text-cyber-text">
                    Conteúdo em desenvolvimento...
                  </p>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                <h3 className="text-white font-semibold mb-4 flex items-center">
                  <FiFileText className="w-5 h-5 mr-2 text-neon-cyan" />
                  Documentos Relacionados
                </h3>
                <div className="space-y-3">
                  <a
                    href="/legal/privacy"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors">
                    → Política de Privacidade
                  </a>
                  <a
                    href="/legal/lgpd"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors">
                    → LGPD Compliance
                  </a>
                  <a
                    href="/legal/cookies"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors">
                    → Política de Cookies
                  </a>
                </div>
              </div>

              <div className="bg-neon-cyan/10 backdrop-blur-sm rounded-2xl p-6 border border-neon-cyan/30">
                <h3 className="text-white font-semibold mb-3">
                  Dúvidas Jurídicas?
                </h3>
                <p className="text-cyber-text text-sm mb-4">
                  Nossa equipe jurídica está disponível para esclarecer questões.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="block w-full text-center bg-neon-cyan text-cyber-bg font-semibold py-2 rounded-lg hover:bg-neon-cyan/80 transition-colors text-sm">
                  Entrar em Contato
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Terms;
