/**
 * @fileoverview Página Termos de Uso do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React, { useState } from 'react';
import {
  FiAlertTriangle,
  FiBook,
  FiCheck,
  FiFileText,
  FiShield,
  Fi<PERSON>ser,
  FiAward,
} from 'react-icons/fi';
import { JsonLdSchema, SEOHead } from '../../components/seo';
import useSEO from '../../hooks/useSEO';

// ============================================================================
// INTERFACES
// ============================================================================

interface TermsProps {
  className?: string;
}

type TabType = 'general' | 'usage' | 'content' | 'liability' | 'rights' | 'final';

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * Página Termos de Uso - Condições gerais de uso dos serviços
 */
export const Terms: React.FC<TermsProps> = () => {
  // SEO para página Terms
  const seoData = useSEO({
    pageType: 'page',
    customTitle: 'Termos de Uso | Blueprint Blog',
    customDescription:
      'Termos e condições de uso do Blueprint Blog - Direitos, responsabilidades e regras para utilização dos nossos serviços',
    customKeywords: [
      'termos de uso',
      'condições',
      'direitos',
      'responsabilidades',
      'regras',
      'serviços',
    ],
  });

  const [activeTab, setActiveTab] = useState<TabType>('general');

  const tabs = [
    { id: 'general' as TabType, label: 'Disposições Gerais', icon: FiBook },
    { id: 'usage' as TabType, label: 'Uso do Site', icon: FiUser },
    { id: 'content' as TabType, label: 'Conteúdo', icon: FiShield },
    { id: 'liability' as TabType, label: 'Responsabilidades', icon: FiAlertTriangle },
    { id: 'rights' as TabType, label: 'Direitos Autorais', icon: FiAward },
    { id: 'final' as TabType, label: 'Disposições Finais', icon: FiCheck },
  ];

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="page"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Legal', url: '/legal' },
          { name: 'Termos de Uso', url: '/legal/terms' },
        ]}
      />

      <div className="min-h-screen bg-cyber-bg py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-neon-cyan/20 rounded-full border border-neon-cyan">
                <FiAward className="w-12 h-12 text-neon-cyan" />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              <span className="text-neon-cyan">Termos de</span> Uso
            </h1>
            <p className="text-cyber-muted text-lg max-w-3xl mx-auto">
              Condições gerais de uso dos serviços do Blueprint Blog
            </p>
            <div className="h-1 w-32 bg-neon-cyan mx-auto rounded-full mt-6"></div>
            <p className="text-cyber-muted mt-4">
              Última atualização: {new Date().toLocaleDateString('pt-BR')}
            </p>
          </div>

          {/* Navigation Tabs */}
          <div className="flex justify-center mb-12 overflow-x-auto">
            <div className="bg-cyber-surface/50 p-2 rounded-full border border-cyber-surface min-w-max">
              <div className="flex space-x-2">
                {tabs.map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => setActiveTab(id)}
                    className={`flex items-center space-x-2 px-4 py-3 rounded-full transition-all whitespace-nowrap ${
                      activeTab === id
                        ? 'bg-neon-cyan text-cyber-bg font-semibold'
                        : 'text-cyber-muted hover:text-white hover:bg-cyber-surface'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {activeTab === 'general' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Disposições Gerais</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        1. Aceitação dos Termos
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Ao acessar e usar o Blueprint Blog, você concorda em cumprir e estar
                        vinculado aos termos e condições de uso descritos neste documento.
                        Se você não concordar com qualquer parte destes termos, não deve
                        usar nosso site.
                      </p>
                      <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                        <h4 className="text-white font-semibold mb-3">
                          Importante:
                        </h4>
                        <ul className="text-cyber-text space-y-2">
                          <li>
                            ✓ Estes termos se aplicam a todos os usuários do site
                          </li>
                          <li>
                            ✓ O uso continuado implica aceitação automática
                          </li>
                          <li>
                            ✓ Reservamo-nos o direito de modificar estes termos
                          </li>
                          <li>
                            ✓ Mudanças entram em vigor imediatamente após publicação
                          </li>
                        </ul>
                      </div>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        2. Definições
                      </h3>
                      <div className="space-y-4">
                        <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-2">
                            "Serviço"
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Refere-se ao site Blueprint Blog e todos os seus recursos,
                            conteúdos e funcionalidades.
                          </p>
                        </div>
                        <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-2">
                            "Usuário"
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Qualquer pessoa que acesse ou use o serviço, seja como
                            visitante ou usuário registrado.
                          </p>
                        </div>
                        <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-2">
                            "Conteúdo"
                          </h4>
                          <p className="text-cyber-text text-sm">
                            Inclui textos, imagens, vídeos, códigos e qualquer material
                            disponibilizado no site.
                          </p>
                        </div>
                      </div>
                    </section>
                  </div>
                </div>
              )}

              {activeTab === 'usage' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Uso do Site</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        3. Condições de Uso
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        O Blueprint Blog é destinado ao compartilhamento de conhecimento sobre
                        tecnologia, programação e inovação. O uso deve ser feito de forma
                        responsável e respeitosa.
                      </p>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="bg-green-500/10 p-4 rounded-lg border border-green-500/30">
                          <h4 className="text-green-400 font-semibold mb-3">✓ Permitido:</h4>
                          <ul className="text-cyber-text text-sm space-y-1">
                            <li>• Leitura e compartilhamento de artigos</li>
                            <li>• Comentários construtivos</li>
                            <li>• Uso educacional do conteúdo</li>
                            <li>• Citação com devida atribuição</li>
                          </ul>
                        </div>
                        <div className="bg-red-500/10 p-4 rounded-lg border border-red-500/30">
                          <h4 className="text-red-400 font-semibold mb-3">✗ Proibido:</h4>
                          <ul className="text-cyber-text text-sm space-y-1">
                            <li>• Spam ou conteúdo malicioso</li>
                            <li>• Violação de direitos autorais</li>
                            <li>• Ataques pessoais ou discriminação</li>
                            <li>• Uso comercial não autorizado</li>
                          </ul>
                        </div>
                      </div>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        4. Conta de Usuário
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Para acessar certas funcionalidades, você pode criar uma conta.
                        Você é responsável por manter a segurança de suas credenciais.
                      </p>
                      <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                        <h4 className="text-white font-semibold mb-3">Responsabilidades:</h4>
                        <ul className="text-cyber-text space-y-2">
                          <li>✓ Fornecer informações precisas e atualizadas</li>
                          <li>✓ Manter a confidencialidade da senha</li>
                          <li>✓ Notificar sobre uso não autorizado</li>
                          <li>✓ Usar a conta apenas para fins legítimos</li>
                        </ul>
                      </div>
                    </section>
                  </div>
                </div>
              )}

              {activeTab === 'content' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Conteúdo</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        5. Propriedade do Conteúdo
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Todo o conteúdo original publicado no Blueprint Blog é de propriedade
                        dos autores e está protegido por direitos autorais.
                      </p>
                      <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                        <h4 className="text-white font-semibold mb-3">Licenciamento:</h4>
                        <p className="text-cyber-text mb-3">
                          O conteúdo é disponibilizado sob licença Creative Commons
                          Attribution-NonCommercial-ShareAlike 4.0 International.
                        </p>
                        <div className="text-cyber-text text-sm space-y-1">
                          <p>• <strong>Atribuição:</strong> Deve creditar o autor original</p>
                          <p>• <strong>Não Comercial:</strong> Uso comercial requer autorização</p>
                          <p>• <strong>Compartilhar Igual:</strong> Derivações sob mesma licença</p>
                        </div>
                      </div>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        6. Conteúdo de Terceiros
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Podemos incluir links e referências a conteúdo de terceiros.
                        Não somos responsáveis por esse conteúdo externo.
                      </p>
                    </section>
                  </div>
                </div>
              )}

              {activeTab === 'liability' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Responsabilidades</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        7. Limitação de Responsabilidade
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        O Blueprint Blog é fornecido "como está" sem garantias de qualquer tipo.
                        Não garantimos que o serviço será ininterrupto ou livre de erros.
                      </p>
                      <div className="bg-amber-500/10 p-6 rounded-lg border border-amber-500/30">
                        <h4 className="text-amber-400 font-semibold mb-3">⚠️ Aviso Importante:</h4>
                        <p className="text-cyber-text text-sm">
                          Em nenhuma circunstância seremos responsáveis por danos diretos,
                          indiretos, incidentais, especiais ou consequenciais resultantes
                          do uso ou incapacidade de usar nosso serviço.
                        </p>
                      </div>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        8. Indenização
                      </h3>
                      <p className="text-cyber-text leading-relaxed">
                        Você concorda em indenizar e isentar o Blueprint Blog de qualquer
                        reivindicação resultante de sua violação destes termos ou uso
                        inadequado do serviço.
                      </p>
                    </section>
                  </div>
                </div>
              )}

              {activeTab === 'rights' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Direitos Autorais</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        9. Política de Direitos Autorais
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Respeitamos os direitos de propriedade intelectual e esperamos
                        que nossos usuários façam o mesmo.
                      </p>
                      <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                        <h4 className="text-white font-semibold mb-3">DMCA Compliance:</h4>
                        <p className="text-cyber-text text-sm mb-3">
                          Se você acredita que seu trabalho foi copiado de forma que
                          constitua violação de direitos autorais, entre em contato:
                        </p>
                        <div className="text-cyber-text text-sm space-y-1">
                          <p>📧 Email: <EMAIL></p>
                          <p>📄 Inclua: Descrição da obra, localização da violação</p>
                          <p>✍️ Assinatura: Física ou eletrônica</p>
                        </div>
                      </div>
                    </section>
                  </div>
                </div>
              )}

              {activeTab === 'final' && (
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                  <h2 className="text-3xl font-bold text-neon-cyan mb-6">Disposições Finais</h2>

                  <div className="space-y-8">
                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        10. Modificações dos Termos
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Reservamo-nos o direito de modificar estes termos a qualquer momento.
                        As alterações entrarão em vigor imediatamente após a publicação.
                      </p>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        11. Lei Aplicável
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Estes termos são regidos pelas leis da República Federativa do Brasil.
                        Qualquer disputa será resolvida nos tribunais competentes do Brasil.
                      </p>
                    </section>

                    <section>
                      <h3 className="text-xl font-semibold text-white mb-4">
                        12. Contato
                      </h3>
                      <p className="text-cyber-text leading-relaxed mb-4">
                        Para questões sobre estes termos, entre em contato:
                      </p>
                      <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                        <div className="text-cyber-text space-y-2">
                          <p>📧 <strong>Email:</strong> <EMAIL></p>
                          <p>🌐 <strong>Site:</strong> blueprintblog.tech</p>
                          <p>📅 <strong>Última atualização:</strong> {new Date().toLocaleDateString('pt-BR')}</p>
                        </div>
                      </div>
                    </section>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                <h3 className="text-white font-semibold mb-4 flex items-center">
                  <FiFileText className="w-5 h-5 mr-2 text-neon-cyan" />
                  Documentos Relacionados
                </h3>
                <div className="space-y-3">
                  <a
                    href="/legal/privacy"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors">
                    → Política de Privacidade
                  </a>
                  <a
                    href="/legal/lgpd"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors">
                    → LGPD Compliance
                  </a>
                  <a
                    href="/legal/cookies"
                    className="block text-cyber-text hover:text-neon-cyan transition-colors">
                    → Política de Cookies
                  </a>
                </div>
              </div>

              <div className="bg-neon-cyan/10 backdrop-blur-sm rounded-2xl p-6 border border-neon-cyan/30">
                <h3 className="text-white font-semibold mb-3">
                  Dúvidas Jurídicas?
                </h3>
                <p className="text-cyber-text text-sm mb-4">
                  Nossa equipe jurídica está disponível para esclarecer questões.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="block w-full text-center bg-neon-cyan text-cyber-bg font-semibold py-2 rounded-lg hover:bg-neon-cyan/80 transition-colors text-sm">
                  Entrar em Contato
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Terms;
