/**
 * @fileoverview Página de Login do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import { clsx } from 'clsx';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  Input,
} from '../../components/ui';
import { useAuth } from '../../contexts/AuthContext';
import type { LoginCredentials } from '../../types';

// ============================================================================
// INTERFACES
// ============================================================================

interface LoginProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página de login com autenticação mock
 */
export const Login: React.FC<LoginProps> = ({ className }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, status } = useAuth();

  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  // Redirect após login
  const from = (location.state as any)?.from?.pathname || '/admin';

  /**
   * Valida os campos do formulário
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!credentials.email) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(credentials.email)) {
      newErrors.email = 'Email inválido';
    }

    if (!credentials.password) {
      newErrors.password = 'Senha é obrigatória';
    } else if (credentials.password.length < 3) {
      newErrors.password = 'Senha deve ter pelo menos 3 caracteres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Submete o formulário de login
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    const success = await login(credentials);

    if (success) {
      navigate(from, { replace: true });
    } else {
      setErrors({
        general: 'Email ou senha incorretos. Verifique suas credenciais e tente novamente.',
      });
    }
  };

  /**
   * Atualiza os campos do formulário
   */
  const handleChange = (
    field: keyof LoginCredentials,
    value: string | boolean
  ) => {
    setCredentials((prev) => ({ ...prev, [field]: value }));
    // Remove erro do campo quando usuário começa a digitar
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div
      className={clsx(
        'min-h-screen flex items-center justify-center p-4',
        className
      )}>
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2">
            <span className="bg-gradient-neon bg-clip-text text-transparent">
              Blueprint Blog
            </span>
          </h1>
          <p className="text-cyber-muted">
            Faça login para acessar o painel administrativo
          </p>
        </div>

        {/* Formulário de Login */}
        <Card neonBorder>
          <CardHeader title="🔐 Login" subtitle="Entre com suas credenciais" />
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Erro geral */}
              {errors.general && (
                <div className="p-3 rounded-lg bg-red-500/20 border border-red-500/30 text-red-400 text-sm">
                  {errors.general}
                </div>
              )}

              {/* Email */}
              <div>
                <Input
                  type="email"
                  label="Email"
                  placeholder="<EMAIL>"
                  value={credentials.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  error={errors.email}
                  leftIcon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                      />
                    </svg>
                  }
                  fullWidth
                  required
                />
              </div>

              {/* Senha */}
              <div>
                <Input
                  type={showPassword ? 'text' : 'password'}
                  label="Senha"
                  placeholder="••••••••"
                  value={credentials.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                  error={errors.password}
                  leftIcon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  }
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-cyber-muted hover:text-cyber-text transition-colors">
                      {showPassword ? (
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                          />
                        </svg>
                      )}
                    </button>
                  }
                  fullWidth
                  required
                />
              </div>

              {/* Lembrar-me */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="rememberMe"
                  checked={credentials.rememberMe}
                  onChange={(e) => handleChange('rememberMe', e.target.checked)}
                  className="w-4 h-4 rounded border-cyber-border bg-cyber-bg text-neon-cyan focus:ring-neon-cyan focus:ring-2"
                />
                <label
                  htmlFor="rememberMe"
                  className="ml-2 text-sm text-cyber-text">
                  Lembrar-me
                </label>
              </div>

              {/* Botão de Login */}
              <Button
                type="submit"
                size="lg"
                loading={status === 'loading'}
                fullWidth>
                {status === 'loading' ? 'Entrando...' : 'Entrar'}
              </Button>
            </form>

            {/* Links */}
            <div className="mt-6 space-y-4">
              <div className="text-center">
                <Link
                  to="/forgot-password"
                  className="text-sm text-neon-cyan hover:text-neon-cyan/80 transition-colors">
                  Esqueceu sua senha?
                </Link>
              </div>

              <div className="text-center text-sm text-cyber-muted">
                Acesso restrito ao administrador do blog
              </div>
            </div>
          </CardContent>
        </Card>



        {/* Voltar ao Site */}
        <div className="mt-6 text-center">
          <Link
            to="/"
            className="text-sm text-cyber-muted hover:text-cyber-text transition-colors">
            ← Voltar ao site
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Login;
