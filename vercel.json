{"buildCommand": "npm install --legacy-peer-deps && npm run build", "installCommand": "npm install --legacy-peer-deps", "framework": "vite", "rewrites": [{"source": "/sitemap.xml", "destination": "/sitemap.xml"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}